import torch
import warnings
from omegaconf import OmegaConf
from models import Dinov2ViT, DinoViT
from torchvision import transforms as T

warnings.filterwarnings('ignore')

class DinoFeatureExtractor:
    """Unified DINO feature extractor for traversability learning.
    
    This class combines DINO/DINOv2 model interface and feature extraction functionality.
    It provides a complete solution for loading DINO models, preprocessing images,
    and extracting features for traversability analysis.
    
    Note: 
        - DINO models will automatically download pretrained weights from official repository
        - DINOv2 models use torch.hub for automatic weight management
        - Weights are cached at: ~/.cache/torch/hub/checkpoints/ (DINO) and ~/.cache/torch/hub/ (DINOv2)
    """
    
    def __init__(self, 
                 device: str = "cuda",
                 backbone: str = "dinov2",
                 input_size: int = 448,
                 backbone_type: str = "vit_base_reg",
                 patch_size: int = 14,
                 projection_type: str = None,  # nonlinear or None
                 dropout_p: float = 0,  # dropout probability
                 cfg: OmegaConf = None):
        """Initialize unified DINO feature extractor.

        Args:
            device (str): Device to run the model on ('cuda' or 'cpu'), defaults to "cuda"
            backbone (str): Type of backbone network ('dino' or 'dinov2'), defaults to "dinov2" 
            input_size (int): Input image size, defaults to 448
            backbone_type (str): Specific type of ViT backbone (e.g. 'vit_base_reg'), defaults to "vit_base_reg"
            patch_size (int): Size of ViT patches, defaults to 14
            projection_type (str): Type of projection head (None or 'nonlinear'), defaults to None
            dropout_p (float): Dropout probability, defaults to 0
            cfg (OmegaConf): Configuration object, defaults to None
        """
        # Load or create configuration
        if cfg is None or cfg.is_empty():
            self._cfg = OmegaConf.create({
                "backbone": backbone,
                "backbone_type": backbone_type,
                "input_size": input_size,
                "patch_size": patch_size,
                "projection_type": projection_type,
                "dropout_p": dropout_p,
            })
        else:
            self._cfg = cfg

        if device == "cuda" and not torch.cuda.is_available():
            print("CUDA not available, falling back to CPU")
            device = "cpu"

        # Initialize DINO model based on configuration
        self._model = self._get_backbone(self._cfg)
        
        # Move model to specified device
        self._model.to(device)
        self._device = device
        
        # Get feature dimension from model
        self._feature_dim = self._model.get_output_feat_dim()
        
        # Setup image preprocessing pipeline
        # Use ImageNet normalization parameters
        normalization = T.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
        self._transform = T.Compose([
            T.Resize(input_size, T.InterpolationMode.NEAREST),
            T.CenterCrop(input_size),
            normalization,
        ])
        
    def _get_backbone(self, cfg):
        """Get the appropriate backbone model based on configuration.
        
        Args:
            cfg (OmegaConf): Configuration object
            
        Returns:
            nn.Module: Initialized backbone model
            
        Raises:
            ValueError: If backbone type is not found or invalid
        """
        if not hasattr(cfg, "backbone"):
            raise ValueError("Could not find 'backbone' option in the config file. Please check it")

        if cfg.backbone == "dino":
            return DinoViT(cfg)
        elif cfg.backbone == "dinov2":
            return Dinov2ViT(cfg)
        else:
            raise ValueError("Backbone {} unavailable".format(cfg.backbone))
        
    def extract_features(self, img_tensors):
        """Extract image features for traversability analysis.

        Args:
            img_tensors (torch.Tensor): Input image tensor of shape (B, 3, H, W)

        Returns:
            torch.Tensor: Feature tensor of shape [B, num_patches, C], where B is batch size, 
                         num_patches is number of patches, and C is feature dimension
        """
        features = self._compute_dino_features(img_tensors)  # [B, H, W, C]
        B, H, W, C = features.shape
        features = features.view(B, H * W, C)  # [B, num_patches, C]
        return features
    
    @torch.no_grad()
    def _compute_dino_features(self, img: torch.tensor):
        """Internal method to compute DINO features with preprocessing.

        Args:
            img (torch.Tensor): Input image tensor of shape (B, 3, H, W)

        Returns:
            torch.Tensor: Feature tensor of shape (B, H, W, C) - per-pixel features
        """
        # Clone input to avoid modifying original tensor
        img_internal = img.clone()
        
        # Apply preprocessing transforms to each image in the batch
        batch_size = img_internal.shape[0]
        processed_imgs = []
        
        for i in range(batch_size):
            # Extract single image (C, H, W)
            single_img = img_internal[i]
            # Apply transforms to single image
            processed_img = self._transform(single_img)
            processed_imgs.append(processed_img)
        
        # Stack processed images back to batch format (B, C, H, W)
        resized_img = torch.stack(processed_imgs, dim=0).to(self._device)
        
        # Extract features using the backbone model
        features = self._model(resized_img)
        return features
    
    @property
    def feature_dim(self):
        """Get feature dimension.

        Returns:
            int: Feature dimension
        """
        return self._feature_dim
        
    @property
    def input_size(self):
        """Get input image size.
        
        Returns:
            int: Input image size
        """
        return self._cfg.input_size

    @property
    def backbone(self):
        """Get backbone type.
        
        Returns:
            str: Backbone type ('dino' or 'dinov2')
        """
        return self._cfg.backbone

    @property
    def backbone_type(self):
        """Get specific ViT backbone type.
        
        Returns:
            str: ViT backbone type (e.g. 'vit_base_reg')
        """
        return self._cfg.backbone_type

    @property
    def vit_patch_size(self):
        """Get ViT patch size.
        
        Returns:
            int: Patch size
        """
        return self._cfg.patch_size
    
    def change_device(self, device):
        """Change model running device.

        Args:
            device (str): New device name ('cuda' or 'cpu')
        """
        self._device = device
        self._model.to(device)