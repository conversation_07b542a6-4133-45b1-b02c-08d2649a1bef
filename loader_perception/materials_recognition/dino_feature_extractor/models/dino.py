import torch
from torch import nn
import numpy as np
from abc import ABC, abstractmethod
from omegaconf import OmegaConf
from . import vits

class Backbone(ABC, nn.Module):
    """
    Base class to provide an interface for new STEGO backbones.

    To add a new backbone for use in STEGO, add a new implementation of this class.
    """

    vit_name_long_to_short = {
        "vit_tiny": "T",
        "vit_small": "S",
        "vit_base": "B",
        "vit_large": "L",
        "vit_huge": "H",
        "vit_giant": "G",
        "neco_on_dinov2r_vit14_model": "N"
    }

    # Initialize the backbone
    @abstractmethod
    def __init__(self, cfg):
        super().__init__()

    # Return the size of features generated by the backbone
    @abstractmethod
    def get_output_feat_dim(self) -> int:
        pass

    # Generate features for the given image
    @abstractmethod
    def forward(self, img):
        pass

    # Returh a name that identifies the type of the backbone
    @abstractmethod
    def get_backbone_name(self):
        pass


class Dinov2ViT(Backbone):
    def __init__(self, cfg):
        super().__init__(cfg)
        self.cfg = cfg
        self.backbone_type = self.cfg.backbone_type
        self.patch_size = 14
        if self.backbone_type == "vit_small":
            self.model = torch.hub.load("facebookresearch/dinov2", "dinov2_vits14")
        elif self.backbone_type == "vit_base":
            self.model = torch.hub.load("facebookresearch/dinov2", "dinov2_vitb14")
        elif self.backbone_type == "vit_small_reg":
            self.model = torch.hub.load("facebookresearch/dinov2", "dinov2_vits14_reg")
        elif self.backbone_type == "vit_base_reg":
            self.model = torch.hub.load("facebookresearch/dinov2", "dinov2_vitb14_reg")
        elif self.backbone_type == "neco_on_dinov2r_vit14_model":
            self.model = torch.hub.load("facebookresearch/dinov2", "dinov2_vitb14_reg")
            url = "https://huggingface.co/FunAILab/NeCo/resolve/main/vit-base/dinov2-architectures/neco_on_dinov2r_vitb14_model.ckpt"
            state_dict = torch.hub.load_state_dict_from_url(url=url)

            # 加载过滤后的状态字典
            missing_keys, unexpected_keys = self.model.load_state_dict(state_dict, strict=False)
            
            # 打印加载信息
            if missing_keys:
                print(f"Missing keys: {missing_keys}")
            if unexpected_keys:
                print(f"Unexpected keys: {unexpected_keys}")
        else:
            raise ValueError("Model type {} unavailable".format(cfg.backbone_type))

        for p in self.model.parameters():
            p.requires_grad = False
        self.model.eval().cuda()
        self.dropout = torch.nn.Dropout2d(p=np.clip(self.cfg.dropout_p, 0.0, 1.0))

        if self.backbone_type == "vit_small":
            self.n_feats = 384
        else:
            self.n_feats = 768

    def get_output_feat_dim(self):
        return self.n_feats

    def forward(self, img):
        self.model.eval()
        with torch.no_grad():
            assert img.shape[2] % self.patch_size == 0
            assert img.shape[3] % self.patch_size == 0

            # get selected layer activations
            feat = self.model.get_intermediate_layers(img)[0]

            feat_h = img.shape[2] // self.patch_size
            feat_w = img.shape[3] // self.patch_size

            image_feat = feat[:, :, :].reshape(feat.shape[0], feat_h, feat_w, -1)

        if self.cfg.dropout_p > 0:
            return self.dropout(image_feat)
        else:
            return image_feat

    def get_backbone_name(self):
        return "DINOv2-" + self.backbone_type + "-" + str(self.patch_size)


class DinoViT(Backbone):
    def __init__(self, cfg):
        super().__init__(cfg)
        self.cfg = cfg
        self.patch_size = self.cfg.patch_size
        self.backbone_type = self.cfg.backbone_type
        self.model = vits.__dict__[self.backbone_type](patch_size=self.patch_size, num_classes=0)
        for p in self.model.parameters():
            p.requires_grad = False
        self.model.eval().cuda()
        self.dropout = torch.nn.Dropout2d(p=np.clip(self.cfg.dropout_p, 0.0, 1.0))

        if self.backbone_type == "vit_small" and self.patch_size == 16:
            url = "dino_deitsmall16_pretrain/dino_deitsmall16_pretrain.pth"
        elif self.backbone_type == "vit_small" and self.patch_size == 8:
            url = "dino_deitsmall8_300ep_pretrain/dino_deitsmall8_300ep_pretrain.pth"
        elif self.backbone_type == "vit_base" and self.patch_size == 16:
            url = "dino_vitbase16_pretrain/dino_vitbase16_pretrain.pth"
        elif self.backbone_type == "vit_base" and self.patch_size == 8:
            url = "dino_vitbase8_pretrain/dino_vitbase8_pretrain.pth"
        else:
            raise ValueError("Model type {} unavailable with patch size {}".format(self.backbone_type, self.patch_size))

        state_dict = torch.hub.load_state_dict_from_url(url="https://dl.fbaipublicfiles.com/dino/" + url)
        self.model.load_state_dict(state_dict, strict=True)

        if self.backbone_type == "vit_small":
            self.n_feats = 384
        else:
            self.n_feats = 768

    def get_output_feat_dim(self):
        return self.n_feats

    def forward(self, img):
        self.model.eval()
        with torch.no_grad():
            assert img.shape[2] % self.patch_size == 0
            assert img.shape[3] % self.patch_size == 0

            # get selected layer activations
            feat, attn, qkv = self.model.get_intermediate_feat(img)
            feat, attn, qkv = feat[0], attn[0], qkv[0]

            feat_h = img.shape[2] // self.patch_size
            feat_w = img.shape[3] // self.patch_size

            image_feat = feat[:, 1:, :].reshape(feat.shape[0], feat_h, feat_w, -1).permute(0, 3, 1, 2)

        if self.cfg.dropout_p > 0:
            return self.dropout(image_feat)
        else:
            return image_feat

    def get_backbone_name(self):
        return "DINO-" + Backbone.vit_name_long_to_short[self.backbone_type] + "-" + str(self.patch_size)
    

if __name__ == "__main__":
    cfg = OmegaConf.create({
        "backbone_type": "neco_on_dinov2r_vit14_model",
        "patch_size": 14,
        "dropout_p": 0.0
    })
    model = Dinov2ViT(cfg)
    print(f"Successfully initialized: {model.get_backbone_name()}")
    print(f"Output feature dimension: {model.get_output_feat_dim()}")
    dummy_img_dino = torch.randn(1, 3, 224, 224)
    if torch.cuda.is_available():
        dummy_img_dino = dummy_img_dino.cuda()
    features_dino = model(dummy_img_dino)
    print(f"DINO Output features shape: {features_dino.shape}")