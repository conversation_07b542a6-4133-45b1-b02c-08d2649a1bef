# DINO Feature Extractor

A Python package for extracting features using DINO and DINOv2 models.

## Installation

```bash
pip install -e .
```

## Usage

```python
from dino_feature_extractor import DinoFeatureExtractor

# Initialize extractor
extractor = DinoFeatureExtractor(
    device="cuda",
    backbone="dinov2",
    backbone_type="vit_base_reg"
)

# Extract features from images
features = extractor.extract_features(image_tensor)
```

## Features

- Support for DINO and DINOv2 models
- Automatic model downloading and caching
- GPU/CPU compatibility
- Configurable model parameters

## Requirements

- Python >= 3.8
- PyTorch >= 1.8.0
- torchvision >= 0.9.0
- omegaconf >= 2.0.0
- numpy >= 1.19.0