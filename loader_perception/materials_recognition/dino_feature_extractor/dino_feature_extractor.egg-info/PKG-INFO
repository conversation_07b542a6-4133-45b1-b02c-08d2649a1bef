Metadata-Version: 2.1
Name: dino-feature-extractor
Version: 1.0.0
Summary: DINO and DINOv2 feature extraction for computer vision tasks
Author: <PERSON><PERSON><PERSON> Yin
Author-email: <EMAIL>
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Topic :: Scientific/Engineering :: Artificial Intelligence
Classifier: Topic :: Computer Vision
Requires-Python: >=3.8
Description-Content-Type: text/markdown
Provides-Extra: dev

# DINO Feature Extractor

A Python package for extracting features using DINO and DINOv2 models.

## Installation

```bash
pip install -e .
```

## Usage

```python
from dino_feature_extractor import DinoFeatureExtractor

# Initialize extractor
extractor = DinoFeatureExtractor(
    device="cuda",
    backbone="dinov2",
    backbone_type="vit_base_reg"
)

# Extract features from images
features = extractor.extract_features(image_tensor)
```

## Features

- Support for DINO and DINOv2 models
- Automatic model downloading and caching
- GPU/CPU compatibility
- Configurable model parameters

## Requirements

- Python >= 3.8
- PyTorch >= 1.8.0
- torchvision >= 0.9.0
- omegaconf >= 2.0.0
- numpy >= 1.19.0
