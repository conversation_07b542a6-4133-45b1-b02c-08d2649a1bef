# DINO Feature Extractor Node

这是一个基于 ROS2 Humble 的 DINO 特征提取节点，用于从相机图像中提取深度视觉特征。

## 功能特性

- **多种 DINO 模型支持**: 支持 DINO 和 DINOv2 两种主干网络
- **灵活的参数配置**: 通过 launch 文件或 YAML 配置文件进行参数管理
- **高效的消息处理**: 避免重复订阅相机信息话题
- **完整的错误处理**: 包含详细的日志记录和异常处理
- **GPU/CPU 兼容**: 自动检测 CUDA 可用性并回退到 CPU

## 依赖项

### ROS2 依赖
- `rclpy`: ROS2 Python 客户端库
- `sensor_msgs`: 传感器消息类型
- `std_msgs`: 标准消息类型
- `cv_bridge`: OpenCV 和 ROS 图像转换
- `loader_perception_msgs`: 自定义消息类型

### Python 依赖
- `torch`: PyTorch 深度学习框架
- `torchvision`: 计算机视觉工具
- `opencv-python`: 计算机视觉库
- `numpy`: 数值计算库
- `omegaconf`: 配置管理

## 安装

1. 确保您在 ROS2 Humble 环境中：
```bash
source /opt/ros/humble/setup.bash
```

2. 构建工作空间：
```bash
cd /path/to/your/workspace
colcon build --packages-select vision_materials_recognition
source install/setup.bash
```

3. 安装 Python 依赖：
```bash
pip install torch torchvision opencv-python numpy omegaconf
```

## 使用方法

### 方法 1: 使用默认参数启动

```bash
ros2 run vision_materials_recognition dino_feature_extractor_node
```

### 方法 2: 使用 Launch 文件（命令行参数）

```bash
# 使用默认参数
ros2 launch vision_materials_recognition dino_feature_extractor.launch.py

# 自定义参数
ros2 launch vision_materials_recognition dino_feature_extractor.launch.py \
  image_topic:=/my_camera/image_raw \
  camera_info_topic:=/my_camera/camera_info \
  device:=cpu \
  backbone:=dinov2 \
  input_size:=224
```

### 方法 3: 使用 YAML 配置文件

```bash
# 使用默认配置文件
ros2 launch vision_materials_recognition dino_feature_extractor_with_config.launch.py

# 使用自定义配置文件
ros2 launch vision_materials_recognition dino_feature_extractor_with_config.launch.py \
  config_file:=/path/to/your/config.yaml

# 启用调试日志
ros2 launch vision_materials_recognition dino_feature_extractor_with_config.launch.py \
  log_level:=debug
```

## 配置参数

### 话题配置
- `image_topic`: RGB 图像话题名称（默认: `/camera/image_raw`）
- `camera_info_topic`: 相机信息话题名称（默认: `/camera/camera_info`）
- `features_topic`: 输出特征话题名称（默认: `/image_features`）

### DINO 模型配置
- `device`: 运行设备（`cuda` 或 `cpu`，默认: `cuda`）
- `backbone`: 主干网络类型（`dino` 或 `dinov2`，默认: `dinov2`）
- `backbone_type`: 具体的 ViT 类型（默认: `vit_base_reg`）
- `input_size`: 输入图像尺寸（默认: `448`）
- `patch_size`: ViT 补丁尺寸（默认: `14`）
- `projection_type`: 投影头类型（空字符串表示 None，或 `nonlinear`）
- `dropout_p`: Dropout 概率（默认: `0.0`）

### 支持的主干网络类型

#### DINOv2 (推荐)
- `vit_small_reg`: 小型模型，速度快
- `vit_base_reg`: 基础模型，平衡性能
- `vit_large_reg`: 大型模型，高精度
- `vit_giant_reg`: 巨型模型，最高精度

#### DINO (原版)
- `vit_small`: 小型 DINO 模型
- `vit_base`: 基础 DINO 模型

## 输入/输出

### 订阅话题
- **图像话题** (`sensor_msgs/Image`): RGB 相机图像
- **相机信息话题** (`sensor_msgs/CameraInfo`): 相机标定信息（仅订阅一次）

### 发布话题
- **特征话题** (`loader_perception_msgs/ImageFeatures`): 提取的 DINO 特征

### 输出特征格式

特征张量的维度为 `[B, num_patches, C]`，其中：
- `B`: 批次大小（通常为 1）
- `num_patches`: 图像补丁数量（取决于输入尺寸和补丁尺寸）
- `C`: 特征维度（取决于模型类型）

例如，对于 448×448 输入图像和 14×14 补丁：
- `num_patches = (448/14) × (448/14) = 32 × 32 = 1024`
- 对于 `vit_base_reg`: `C = 768`

## 性能建议

### 高性能配置（GPU 推荐）
```yaml
device: "cuda"
backbone: "dinov2"
backbone_type: "vit_large_reg"
input_size: 518
```

### CPU 友好配置
```yaml
device: "cpu"
backbone: "dinov2"
backbone_type: "vit_small_reg"
input_size: 224
```

### 实时应用配置
```yaml
device: "cuda"
backbone: "dinov2"
backbone_type: "vit_base_reg"
input_size: 224
```

## 故障排除

### 常见问题

1. **CUDA 内存不足**
   - 解决方案：使用更小的模型或降低输入尺寸
   - 或者切换到 CPU 模式

2. **模型下载失败**
   - 确保网络连接正常
   - 检查防火墙设置
   - 模型会自动缓存到 `~/.cache/torch/hub/`

3. **导入错误**
   - 确保所有 Python 依赖都已正确安装
   - 检查 PyTorch 版本兼容性

### 调试

启用调试日志以获取更多信息：
```bash
ros2 launch vision_materials_recognition dino_feature_extractor_with_config.launch.py log_level:=debug
```

### 检查话题

验证节点是否正常工作：
```bash
# 查看所有话题
ros2 topic list

# 查看特征话题信息
ros2 topic info /image_features

# 查看特征消息
ros2 topic echo /image_features
```

## 示例应用

### 获取图像特征用于下游任务

```python
import rclpy
from rclpy.node import Node
from loader_perception_msgs.msg import ImageFeatures
import numpy as np

class FeatureSubscriber(Node):
    def __init__(self):
        super().__init__('feature_subscriber')
        self.subscription = self.create_subscription(
            ImageFeatures,
            '/image_features',
            self.feature_callback,
            10
        )
    
    def feature_callback(self, msg):
        # 重建特征张量
        dims = msg.features.layout.dim
        shape = [dim.size for dim in dims]
        features = np.array(msg.features.data).reshape(shape)
        
        self.get_logger().info(f"收到特征，形状: {features.shape}")
        
        # 在这里使用特征进行下游任务
        # 例如：分类、聚类、相似性计算等
```

## 许可证

TODO: License declaration

## 维护者

- 维护者: root
- 邮箱: <EMAIL> 