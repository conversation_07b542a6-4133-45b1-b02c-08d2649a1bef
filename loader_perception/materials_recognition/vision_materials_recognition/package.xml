<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>vision_materials_recognition</name>
  <version>0.0.0</version>
  <description>Vision-based materials recognition for loader perception using DINO features</description>
  <maintainer email="<EMAIL>">root</maintainer>
  <license>TODO: License declaration</license>

  <!-- Build dependencies -->
  <buildtool_depend>ament_python</buildtool_depend>

  <!-- Runtime dependencies -->
  <depend>rclpy</depend>
  <depend>std_msgs</depend>
  <depend>sensor_msgs</depend>
  <depend>cv_bridge</depend>
  <depend>loader_perception_msgs</depend>
  
  <!-- Python dependencies (will be handled by pip/requirements.txt) -->
  <!-- torch, torchvision, opencv-python, numpy, omegaconf -->

  <test_depend>ament_copyright</test_depend>
  <test_depend>ament_flake8</test_depend>
  <test_depend>ament_pep257</test_depend>
  <test_depend>python3-pytest</test_depend>

  <export>
    <build_type>ament_python</build_type>
  </export>
</package>
