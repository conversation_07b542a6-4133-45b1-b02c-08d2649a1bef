#!/usr/bin/env python3

"""
DINO Feature Extractor ROS2 Node

This node subscribes to RGB camera images and camera info topics,
extracts DINO features using the DinoFeatureExtractor class,
and publishes the extracted features as ImageFeatures messages.

Author: Maoliang Yin
Date: 2025-06-03
"""

import rclpy
from rclpy.node import Node
from rclpy.parameter import Parameter
from rcl_interfaces.msg import ParameterDescriptor
import torch
import numpy as np

# ROS2 message imports
from sensor_msgs.msg import Image, CameraInfo
from std_msgs.msg import MultiArrayDimension
from loader_perception_msgs.msg import ImageFeatures

# Import DINO feature extractor
from dino_feature_extractor import DinoFeatureExtractor

# Import ROS image conversion utility
from .utils.ros_converter import ros_image_to_torch


class DinoFeatureExtractorNode(Node):
    """
    ROS2 Node for extracting DINO features from camera images.
    
    This node subscribes to camera image and camera info topics,
    processes the images using a DINO feature extractor,
    and publishes the extracted features.
    """
    
    def __init__(self):
        """Initialize the DINO feature extractor node."""
        super().__init__('dino_feature_extractor_node')
        
        # Flag to track camera info subscription status
        self._camera_info_received = False
        self._camera_info = None
        
        # Initialize DINO feature extractor (will be set up after parameters are loaded)
        self._feature_extractor = None
        
        # Declare and get parameters with descriptors for better documentation
        self._declare_parameters()
        self._load_parameters()
        
        # Initialize DINO feature extractor with loaded parameters
        self._setup_feature_extractor()
        
        # Create subscribers and publishers
        self._setup_ros_interface()
        
        self.get_logger().info(f"DINO Feature Extractor Node initialized successfully")
        self.get_logger().info(f"Using device: {self._device}")
        self.get_logger().info(f"Using backbone: {self._backbone} ({self._backbone_type})")
        self.get_logger().info(f"Input size: {self._input_size}x{self._input_size}")
        
    def _declare_parameters(self):
        """Declare all node parameters with descriptions and default values."""
        
        # Camera topics
        self.declare_parameter(
            'image_topic', 
            '/camera/image_raw',
            ParameterDescriptor(description='RGB camera image topic name')
        )
        
        self.declare_parameter(
            'camera_info_topic', 
            '/camera/camera_info',
            ParameterDescriptor(description='Camera info topic name')
        )
        
        # Output topic
        self.declare_parameter(
            'features_topic', 
            '/image_features',
            ParameterDescriptor(description='Output image features topic name')
        )
        
        # DINO model parameters
        self.declare_parameter(
            'device', 
            'cuda',
            ParameterDescriptor(description='Device to run the model on (cuda/cpu)')
        )
        
        self.declare_parameter(
            'backbone', 
            'dinov2',
            ParameterDescriptor(description='DINO backbone type (dino/dinov2)')
        )
        
        self.declare_parameter(
            'backbone_type', 
            'vit_base_reg',
            ParameterDescriptor(description='Specific ViT backbone type')
        )
        
        self.declare_parameter(
            'input_size', 
            448,
            ParameterDescriptor(description='Input image size for DINO model')
        )
        
        self.declare_parameter(
            'patch_size', 
            14,
            ParameterDescriptor(description='ViT patch size')
        )
        
        self.declare_parameter(
            'projection_type', 
            '',  # Empty string for None
            ParameterDescriptor(description='Projection head type (empty for None, "nonlinear" for nonlinear)')
        )
        
        self.declare_parameter(
            'dropout_p', 
            0.0,
            ParameterDescriptor(description='Dropout probability')
        )
        
    def _load_parameters(self):
        """Load all parameters from the parameter server."""
        
        # Camera topics
        self._image_topic = self.get_parameter('image_topic').get_parameter_value().string_value
        self._camera_info_topic = self.get_parameter('camera_info_topic').get_parameter_value().string_value
        self._features_topic = self.get_parameter('features_topic').get_parameter_value().string_value
        
        # DINO model parameters
        self._device = self.get_parameter('device').get_parameter_value().string_value
        self._backbone = self.get_parameter('backbone').get_parameter_value().string_value
        self._backbone_type = self.get_parameter('backbone_type').get_parameter_value().string_value
        self._input_size = self.get_parameter('input_size').get_parameter_value().integer_value
        self._patch_size = self.get_parameter('patch_size').get_parameter_value().integer_value
        
        # Handle projection_type (convert empty string to None)
        projection_type_str = self.get_parameter('projection_type').get_parameter_value().string_value
        self._projection_type = None if projection_type_str == '' else projection_type_str
        
        self._dropout_p = self.get_parameter('dropout_p').get_parameter_value().double_value
        
        self.get_logger().info("Parameters loaded successfully:")
        self.get_logger().info(f"  Image topic: {self._image_topic}")
        self.get_logger().info(f"  Camera info topic: {self._camera_info_topic}")
        self.get_logger().info(f"  Features topic: {self._features_topic}")
        self.get_logger().info(f"  Device: {self._device}")
        self.get_logger().info(f"  Backbone: {self._backbone} ({self._backbone_type})")
        
    def _setup_feature_extractor(self):
        """Initialize the DINO feature extractor with loaded parameters."""
        try:
            self._feature_extractor = DinoFeatureExtractor(
                device=self._device,
                backbone=self._backbone,
                input_size=self._input_size,
                backbone_type=self._backbone_type,
                patch_size=self._patch_size,
                projection_type=self._projection_type,
                dropout_p=self._dropout_p
            )
            
            self.get_logger().info(f"DINO feature extractor initialized successfully")
            self.get_logger().info(f"Feature dimension: {self._feature_extractor.feature_dim}")
            
        except Exception as e:
            self.get_logger().error(f"Failed to initialize DINO feature extractor: {str(e)}")
            raise e
            
    def _setup_ros_interface(self):
        """Set up ROS2 subscribers and publishers."""
        
        # Create publisher for image features
        self._features_publisher = self.create_publisher(
            ImageFeatures,
            self._features_topic,
            10
        )
        
        # Create subscriber for camera info (only subscribe once)
        self._camera_info_subscriber = self.create_subscription(
            CameraInfo,
            self._camera_info_topic,
            self._camera_info_callback,
            10
        )
        
        # Create subscriber for RGB images
        self._image_subscriber = self.create_subscription(
            Image,
            self._image_topic,
            self._image_callback,
            10
        )
        
        self.get_logger().info("ROS2 interface set up successfully")
        self.get_logger().info(f"Subscribed to: {self._image_topic}")
        self.get_logger().info(f"Subscribed to: {self._camera_info_topic}")
        self.get_logger().info(f"Publishing to: {self._features_topic}")
        
    def _camera_info_callback(self, msg: CameraInfo):
        """
        Callback function for camera info messages.
        Only processes the first message to avoid repeated subscriptions.
        
        Args:
            msg (CameraInfo): Camera information message
        """
        if not self._camera_info_received:
            self._camera_info = msg
            self._camera_info_received = True
            
            self.get_logger().info("Camera info received successfully")
            self.get_logger().info(f"Camera resolution: {msg.width}x{msg.height}")
            
            # Destroy the camera info subscriber to avoid repeated subscriptions
            self.destroy_subscription(self._camera_info_subscriber)
            self.get_logger().info("Camera info subscriber destroyed to avoid repetition")
            
    def _image_callback(self, msg: Image):
        """
        Callback function for camera image messages.
        Processes the image and extracts DINO features.
        
        Args:
            msg (Image): ROS2 Image message
        """
        try:
            # Convert ROS image to PyTorch tensor using the utility function
            # This handles the conversion from ROS Image to (C, H, W) format tensor
            img_tensor = ros_image_to_torch(
                ros_img=msg, 
                desired_encoding='rgb8', 
                device=self._feature_extractor._device
            )
            
            # Add batch dimension: (C, H, W) -> (1, C, H, W)
            img_batch = img_tensor.unsqueeze(0)
            
            self.get_logger().debug(f"Input image tensor shape: {img_batch.shape}")
            
            # Extract DINO features
            # Features shape: [B, num_patches, C]
            dense_feat = self._feature_extractor.extract_features(img_batch)
            
            self.get_logger().debug(f"Extracted features shape: {dense_feat.shape}")
            
            # Publish the extracted features
            self._publish_features(msg, dense_feat)
            
        except Exception as e:
            self.get_logger().error(f"Error processing image: {str(e)}")
            
    def _publish_features(self, image_msg: Image, dense_feat: torch.Tensor):
        """
        Publish extracted features as ImageFeatures message.
        
        Args:
            image_msg (Image): Original image message for header information
            dense_feat (torch.Tensor): Extracted features tensor of shape [B, num_patches, C]
        """
        try:
            # Create ImageFeatures message
            msg = ImageFeatures()
            msg.header = image_msg.header
            
            # Convert features to numpy array
            feat_np = dense_feat.cpu().numpy()
            
            self.get_logger().debug(f"Features numpy shape: {feat_np.shape}")
            
            # Set MultiArray dimensions information
            # Dimension 0: batch size
            dim1 = MultiArrayDimension()
            dim1.label = "batch"
            dim1.size = feat_np.shape[0]
            dim1.stride = feat_np.shape[0] * feat_np.shape[1] * feat_np.shape[2]
            
            # Dimension 1: number of patches/features
            dim2 = MultiArrayDimension()
            dim2.label = "num_features"
            dim2.size = feat_np.shape[1]
            dim2.stride = feat_np.shape[1] * feat_np.shape[2]

            # Dimension 2: feature dimension
            dim3 = MultiArrayDimension()
            dim3.label = "feat_dim"
            dim3.size = feat_np.shape[2]
            dim3.stride = feat_np.shape[2]
            
            # Set features data and layout
            msg.features.data = feat_np.flatten().tolist()
            msg.features.layout.dim = [dim1, dim2, dim3]
            
            # Publish the message
            self._features_publisher.publish(msg)
            
            self.get_logger().debug(f"Published features with {feat_np.shape[1]} patches, "
                                   f"each with {feat_np.shape[2]} dimensions")
            
        except Exception as e:
            self.get_logger().error(f"Error publishing features: {str(e)}")


def main(args=None):
    """Main function to run the DINO feature extractor node."""
    
    # Initialize ROS2
    rclpy.init(args=args)
    
    try:
        # Create and run the node
        node = DinoFeatureExtractorNode()
        
        node.get_logger().info("DINO Feature Extractor Node is running...")
        
        # Spin the node
        rclpy.spin(node)
        
    except KeyboardInterrupt:
        print("\nShutdown requested by user")
    except Exception as e:
        print(f"Error running node: {str(e)}")
    finally:
        # Clean shutdown
        if 'node' in locals():
            node.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main()
