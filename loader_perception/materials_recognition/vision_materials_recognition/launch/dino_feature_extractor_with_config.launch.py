#!/usr/bin/env python3

"""
Launch file for DINO Feature Extractor Node with YAML configuration

This launch file starts the DINO feature extractor node using parameters from a YAML file.

Author: Maoliang Yin
Date: 2025-06-03
"""

import os
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, LogInfo
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare


def generate_launch_description():
    """Generate launch description for DINO feature extractor node using YAML config."""
    
    # Get package path
    pkg_share = FindPackageShare('vision_materials_recognition')
    
    # Declare launch argument for config file
    declare_config_file = DeclareLaunchArgument(
        'config_file',
        default_value=PathJoinSubstitution([
            pkg_share,
            'config',
            'dino_params.yaml'
        ]),
        description='Path to the YAML configuration file'
    )
    
    declare_log_level = DeclareLaunchArgument(
        'log_level',
        default_value='info',
        description='Logging level (debug, info, warn, error)'
    )
    
    # DINO Feature Extractor Node with YAML configuration
    dino_feature_extractor_node = Node(
        package='vision_materials_recognition',
        executable='dino_feature_extractor_node',
        name='dino_feature_extractor_node',
        output='screen',
        parameters=[LaunchConfiguration('config_file')],
        arguments=['--ros-args', '--log-level', LaunchConfiguration('log_level')],
        remappings=[
            # Example remappings if needed
            # ('/camera/image_raw', '/your_camera/image_raw'),
            # ('/camera/camera_info', '/your_camera/camera_info'),
        ]
    )
    
    # Log information about the launch
    log_info = LogInfo(
        msg="Starting DINO Feature Extractor Node with YAML configuration file"
    )
    
    return LaunchDescription([
        # Declare launch arguments
        declare_config_file,
        declare_log_level,
        
        # Log launch information
        log_info,
        
        # Start the node
        dino_feature_extractor_node,
    ])


# Example usage in terminal:
# ros2 launch vision_materials_recognition dino_feature_extractor_with_config.launch.py
#
# With custom config file:
# ros2 launch vision_materials_recognition dino_feature_extractor_with_config.launch.py \
#   config_file:=/path/to/your/custom_config.yaml
#
# With debug logging:
# ros2 launch vision_materials_recognition dino_feature_extractor_with_config.launch.py \
#   log_level:=debug 