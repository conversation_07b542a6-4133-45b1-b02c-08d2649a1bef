#!/usr/bin/env python3

"""
Launch file for DINO Feature Extractor Node

This launch file starts the DINO feature extractor node with configurable parameters.
It demonstrates how to pass parameters from a launch file to the ROS2 node.

Author: ROS2 Humble Assistant
Date: 2024
"""

import os
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, LogInfo
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare


def generate_launch_description():
    """Generate launch description for DINO feature extractor node."""
    
    # Declare launch arguments with default values
    declare_image_topic = DeclareLaunchArgument(
        'image_topic',
        default_value='/camera/image_raw',
        description='RGB camera image topic name'
    )
    
    declare_camera_info_topic = DeclareLaunchArgument(
        'camera_info_topic',
        default_value='/camera/camera_info',
        description='Camera info topic name'
    )
    
    declare_features_topic = DeclareLaunchArgument(
        'features_topic',
        default_value='/image_features',
        description='Output image features topic name'
    )
    
    declare_device = DeclareLaunchArgument(
        'device',
        default_value='cuda',
        description='Device to run the model on (cuda/cpu)'
    )
    
    declare_backbone = DeclareLaunchArgument(
        'backbone',
        default_value='dinov2',
        description='DINO backbone type (dino/dinov2)'
    )
    
    declare_backbone_type = DeclareLaunchArgument(
        'backbone_type',
        default_value='vit_base_reg',
        description='Specific ViT backbone type'
    )
    
    declare_input_size = DeclareLaunchArgument(
        'input_size',
        default_value='448',
        description='Input image size for DINO model'
    )
    
    declare_patch_size = DeclareLaunchArgument(
        'patch_size',
        default_value='14',
        description='ViT patch size'
    )
    
    declare_projection_type = DeclareLaunchArgument(
        'projection_type',
        default_value='',
        description='Projection head type (empty for None, "nonlinear" for nonlinear)'
    )
    
    declare_dropout_p = DeclareLaunchArgument(
        'dropout_p',
        default_value='0.0',
        description='Dropout probability'
    )
    
    # DINO Feature Extractor Node
    dino_feature_extractor_node = Node(
        package='vision_materials_recognition',
        executable='dino_feature_extractor_node',
        name='dino_feature_extractor_node',
        output='screen',
        parameters=[{
            'image_topic': LaunchConfiguration('image_topic'),
            'camera_info_topic': LaunchConfiguration('camera_info_topic'),
            'features_topic': LaunchConfiguration('features_topic'),
            'device': LaunchConfiguration('device'),
            'backbone': LaunchConfiguration('backbone'),
            'backbone_type': LaunchConfiguration('backbone_type'),
            'input_size': LaunchConfiguration('input_size'),
            'patch_size': LaunchConfiguration('patch_size'),
            'projection_type': LaunchConfiguration('projection_type'),
            'dropout_p': LaunchConfiguration('dropout_p'),
        }],
        remappings=[
            # Example remappings if needed
            # ('/camera/image_raw', '/your_camera/image_raw'),
            # ('/camera/camera_info', '/your_camera/camera_info'),
        ]
    )
    
    # Log information about the launch
    log_info = LogInfo(
        msg="Starting DINO Feature Extractor Node with the following configuration:\n"
            "  Image topic: [image_topic]\n"
            "  Camera info topic: [camera_info_topic]\n"
            "  Features topic: [features_topic]\n"
            "  Device: [device]\n"
            "  Backbone: [backbone] ([backbone_type])\n"
            "  Input size: [input_size]x[input_size]\n"
            "  Patch size: [patch_size]\n"
            "  Projection type: [projection_type]\n"
            "  Dropout probability: [dropout_p]"
    )
    
    return LaunchDescription([
        # Declare all launch arguments
        declare_image_topic,
        declare_camera_info_topic,
        declare_features_topic,
        declare_device,
        declare_backbone,
        declare_backbone_type,
        declare_input_size,
        declare_patch_size,
        declare_projection_type,
        declare_dropout_p,
        
        # Log launch information
        log_info,
        
        # Start the node
        dino_feature_extractor_node,
    ])


# Example usage in terminal:
# ros2 launch vision_materials_recognition dino_feature_extractor.launch.py
#
# With custom parameters:
# ros2 launch vision_materials_recognition dino_feature_extractor.launch.py \
#   image_topic:=/my_camera/image_raw \
#   camera_info_topic:=/my_camera/camera_info \
#   device:=cpu \
#   backbone:=dino \
#   input_size:=224 