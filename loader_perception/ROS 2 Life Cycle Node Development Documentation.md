# ROS 2 生命周期节点开发文档

## 1. 概述

本文档提供了如何开发基于ROS 2生命周期节点的指南和最佳实践，使用提供的YOLO目标检测节点作为参考实例。生命周期节点提供了更精细的节点状态管理，特别适合需要有序启动和关闭的复杂机器人系统。

## 2. 生命周期节点基础

### 2.1 生命周期状态

ROS 2生命周期节点遵循以下主要状态：

- **未配置(Unconfigured)**: 节点初始创建状态
- **非活动(Inactive)**: 节点配置完成但未激活
- **活动(Active)**: 节点完全运行中
- **最终(Finalized)**: 节点关闭状态

### 2.2 状态转换回调函数

每个生命周期节点需要实现以下状态转换回调：

- `on_configure`: 未配置 → 非活动
- `on_activate`: 非活动 → 活动
- `on_deactivate`: 活动 → 非活动
- `on_cleanup`: 非活动 → 未配置
- `on_shutdown`: 任何状态 → 最终

## 3. 节点开发指南

### 3.1 节点结构设计

生命周期节点应遵循以下结构设计原则：

1. 继承`LifecycleNode`基类
2. 在构造函数中声明参数
3. 在适当的生命周期回调中创建和管理资源
4. 遵循资源管理的最佳实践

### 3.2 资源管理策略

| 资源类型 | 创建时机 | 销毁时机 |
|---------|---------|---------|
| 参数声明 | 构造函数 | 自动管理 |
| 参数获取 | `on_configure` | 不需要手动释放 |
| 生命周期发布者 | `on_configure` | `on_cleanup` |
| 订阅者 | `on_activate` | `on_deactivate` |
| 服务 | `on_activate` | `on_deactivate` |
| 计算资源(如模型) | `on_activate` | `on_deactivate` |

### 3.3 发布者和订阅者设置

#### 发布者设置

```python
# 在on_configure中创建生命周期发布者
self._pub = self.create_lifecycle_publisher(MessageType, "topic_name", qos_depth)

# 在on_cleanup中销毁发布者
self.destroy_publisher(self._pub)
```

#### 订阅者设置

```python
# 在on_activate中创建订阅者
self._sub = self.create_subscription(
    MessageType, "topic_name", self.callback_function, qos_profile
)

# 在on_deactivate中销毁订阅者
self.destroy_subscription(self._sub)
self._sub = None
```

## 4. 完整开发流程

### 4.1 初始化阶段 (构造函数)

- 调用父类构造函数
- 声明所有参数
- 初始化类变量
- **不要**在此阶段创建发布者、订阅者或启动计算资源

### 4.2 配置阶段 (on_configure)

- 获取参数值
- 创建QoS配置文件
- 创建生命周期发布者
- 初始化可重用资源(如CV桥接器)
- **返回**转换结果状态

### 4.3 激活阶段 (on_activate)

- 激活生命周期发布者
- 加载计算资源(如ML模型)
- 创建订阅者
- 创建服务
- 启动定时器
- **返回**转换结果状态

### 4.4 停用阶段 (on_deactivate)

- 释放计算资源
- 销毁订阅者
- 销毁服务
- 停止定时器
- **返回**转换结果状态

### 4.5 清理阶段 (on_cleanup)

- 销毁发布者
- 清理其他资源
- **返回**转换结果状态

### 4.6 关闭阶段 (on_shutdown)

- 执行最终清理
- **返回**转换结果状态

## 5. 代码模板

```python
import rclpy
from rclpy.lifecycle import LifecycleNode, TransitionCallbackReturn, LifecycleState
from rclpy.qos import QoSProfile, QoSReliabilityPolicy, QoSHistoryPolicy, QoSDurabilityPolicy
from std_msgs.msg import String  # 根据实际需要导入消息类型


class MyLifecycleNode(LifecycleNode):
    """
    模板生命周期节点类
    
    实现了ROS 2生命周期节点的基本结构和状态管理
    """
    
    def __init__(self):
        """初始化节点，声明所有必要的参数"""
        super().__init__("my_lifecycle_node")
        
        # 声明参数
        self.declare_parameter("parameter1", "default_value")
        self.declare_parameter("parameter2", 10)
        self.declare_parameter("enable", True)
        
        # 初始化类变量 (不要创建发布者或订阅者)
        self._pub = None
        self._sub = None
        self._timer = None
        
    def on_configure(self, state: LifecycleState) -> TransitionCallbackReturn:
        """配置节点状态的回调函数"""
        self.get_logger().info(f"[{self.get_name()}] 正在配置...")
        
        # 获取参数
        self.param1 = self.get_parameter("parameter1").get_parameter_value().string_value
        self.param2 = self.get_parameter("parameter2").get_parameter_value().integer_value
        self.enable = self.get_parameter("enable").get_parameter_value().bool_value
        
        # 创建QoS配置
        self.qos_profile = QoSProfile(
            reliability=QoSReliabilityPolicy.RELIABLE,
            history=QoSHistoryPolicy.KEEP_LAST,
            durability=QoSDurabilityPolicy.VOLATILE,
            depth=10,
        )
        
        # 创建生命周期发布者
        self._pub = self.create_lifecycle_publisher(String, "output_topic", 10)
        
        # 必须调用父类方法
        super().on_configure(state)
        self.get_logger().info(f"[{self.get_name()}] 配置完成")
        
        return TransitionCallbackReturn.SUCCESS
        
    def on_activate(self, state: LifecycleState) -> TransitionCallbackReturn:
        """激活节点状态的回调函数"""
        self.get_logger().info(f"[{self.get_name()}] 正在激活...")
        
        # 激活发布者
        # 注意: 生命周期发布者需要显式激活
        self._pub.on_activate()
        
        # 加载资源（如ML模型）
        try:
            # 这里加载资源
            pass
        except Exception as e:
            self.get_logger().error(f"加载资源失败: {e}")
            return TransitionCallbackReturn.ERROR
        
        # 创建订阅者
        self._sub = self.create_subscription(
            String, "input_topic", self.topic_callback, self.qos_profile
        )
        
        # 创建服务
        self._service = self.create_service(
            ServiceType, "service_name", self.service_callback
        )
        
        # 创建定时器
        self._timer = self.create_timer(1.0, self.timer_callback)
        
        super().on_activate(state)
        self.get_logger().info(f"[{self.get_name()}] 激活完成")
        
        return TransitionCallbackReturn.SUCCESS
        
    def on_deactivate(self, state: LifecycleState) -> TransitionCallbackReturn:
        """停用节点状态的回调函数"""
        self.get_logger().info(f"[{self.get_name()}] 正在停用...")
        
        # 释放资源
        # ...
        
        # 停用发布者
        self._pub.on_deactivate()
        
        # 销毁定时器
        if self._timer:
            self.destroy_timer(self._timer)
            self._timer = None
        
        # 销毁订阅者
        if self._sub:
            self.destroy_subscription(self._sub)
            self._sub = None
            
        # 销毁服务
        if self._service:
            self.destroy_service(self._service)
            self._service = None
        
        super().on_deactivate(state)
        self.get_logger().info(f"[{self.get_name()}] 停用完成")
        
        return TransitionCallbackReturn.SUCCESS
        
    def on_cleanup(self, state: LifecycleState) -> TransitionCallbackReturn:
        """清理节点资源的回调函数"""
        self.get_logger().info(f"[{self.get_name()}] 正在清理...")
        
        # 销毁发布者
        if self._pub:
            self.destroy_publisher(self._pub)
            self._pub = None
            
        # 其他资源清理
        # ...
        
        super().on_cleanup(state)
        self.get_logger().info(f"[{self.get_name()}] 清理完成")
        
        return TransitionCallbackReturn.SUCCESS
        
    def on_shutdown(self, state: LifecycleState) -> TransitionCallbackReturn:
        """关闭节点的回调函数"""
        self.get_logger().info(f"[{self.get_name()}] 正在关闭...")
        
        # 最终清理
        # ...
        
        super().on_shutdown(state)
        self.get_logger().info(f"[{self.get_name()}] 关闭完成")
        
        return TransitionCallbackReturn.SUCCESS
        
    def topic_callback(self, msg):
        """订阅者回调函数"""
        # 处理接收到的消息
        self.get_logger().info(f"收到消息: {msg.data}")
        
        # 只有在节点激活时才发布消息
        if self._pub.is_activated:
            output_msg = String()
            output_msg.data = f"处理结果: {msg.data}"
            self._pub.publish(output_msg)
            
    def timer_callback(self):
        """定时器回调函数"""
        # 只有在节点激活且启用时执行
        if self._pub.is_activated and self.enable:
            # 执行定期任务
            # ...
            
    def service_callback(self, request, response):
        """服务回调函数"""
        # 处理服务请求
        # ...
        return response


def main():
    """主函数"""
    rclpy.init()
    node = MyLifecycleNode()
    
    # 自动配置和激活节点
    node.trigger_configure()
    node.trigger_activate()
    
    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        # 优雅关闭
        node.trigger_deactivate()
        node.trigger_cleanup()
        node.trigger_shutdown()
        
        node.destroy_node()
        rclpy.shutdown()


if __name__ == "__main__":
    main()
```

## 6. 最佳实践

### 6.1 资源管理

- 严格遵循生命周期状态分配资源
- 确保在适当的状态下释放资源
- 使用try-except块处理资源加载/释放的异常
- 在日志中清晰记录资源管理操作

### 6.2 发布者和订阅者

- 生命周期发布者需要显式激活和停用
- 在使用发布者前检查其激活状态
- 订阅者可以在非活动状态下接收消息，必要时使用标志控制

### 6.3 状态转换处理

- 总是返回适当的转换结果
- 如果操作失败，返回ERROR状态
- 确保所有父类方法被调用
- 记录详细的状态转换日志

### 6.4 命名和组织

- 使用一致的命名规范
- 对类变量使用下划线前缀
- 分组相关功能
- 提供详细的代码注释

## 7. 节点启动和管理

### 7.1 手动触发状态转换

```python
node = MyLifecycleNode()
node.trigger_configure()
node.trigger_activate()
```

### 7.2 使用生命周期管理器

可以创建专用的生命周期管理器节点来控制其他生命周期节点：

```python
class LifecycleManager(Node):
    def __init__(self):
        super().__init__('lifecycle_manager')
        self.managed_nodes = ['node1', 'node2']
        
    def configure_nodes(self):
        for node_name in self.managed_nodes:
            # 调用change_state服务配置节点
            # ...
```

### 7.3 使用命令行工具

```bash
# 查看节点状态
ros2 lifecycle get /node_name

# 触发状态转换
ros2 lifecycle set /node_name configure
ros2 lifecycle set /node_name activate
ros2 lifecycle set /node_name deactivate
ros2 lifecycle set /node_name cleanup
ros2 lifecycle set /node_name shutdown
```