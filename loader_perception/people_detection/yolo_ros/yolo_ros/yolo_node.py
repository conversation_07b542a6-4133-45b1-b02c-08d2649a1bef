import cv2
from typing import List, Dict
from cv_bridge import CvBridge

import rclpy
from rclpy.qos import QoSProfile
from rclpy.qos import QoSHistoryPolicy
from rclpy.qos import QoSDurabilityPolicy
from rclpy.qos import QoSReliabilityPolicy
from rclpy.lifecycle import LifecycleNode
from rclpy.lifecycle import TransitionCallbackReturn
from rclpy.lifecycle import LifecycleState

import torch
from ultralytics import YOLO, YOLOWorld
from ultralytics.engine.results import Results
from ultralytics.engine.results import Boxes
from ultralytics.engine.results import Masks
from ultralytics.engine.results import Keypoints

from std_srvs.srv import SetBool
from sensor_msgs.msg import Image
from yolo_msgs.msg import Point2D
from yolo_msgs.msg import BoundingBox2D
from yolo_msgs.msg import Mask
from yolo_msgs.msg import KeyPoint2D
from yolo_msgs.msg import KeyPoint2DArray
from yolo_msgs.msg import Detection
from yolo_msgs.msg import DetectionArray
from yolo_msgs.srv import SetClasses


class YoloNode(LifecycleNode):
    """
    YOLO 目标检测 ROS 节点类

    该节点通过 YOLO 或 YOLOWorld 模型处理图像，并发布检测结果
    实现了 ROS 2 生命周期节点的状态管理
    """

    def __init__(self) -> None:
        """
        初始化 YOLO 节点，声明所有必要的参数
        """
        super().__init__("yolo_node")

        # 声明参数
        self.declare_parameter("model_type", "YOLO")  # 模型类型：YOLO 或 YOLOWorld
        self.declare_parameter("model", "yolov8m.pt")  # 模型文件路径
        self.declare_parameter("device", "cuda:0")  # 推理设备
        self.declare_parameter("yolo_encoding", "bgr8")  # 图像编码格式
        self.declare_parameter("enable", True)  # 是否启用检测
        self.declare_parameter("image_reliability", QoSReliabilityPolicy.BEST_EFFORT)  # 图像消息 QoS 可靠性策略

        # 推理相关参数
        self.declare_parameter("threshold", 0.5)  # 检测阈值
        self.declare_parameter("iou", 0.5)  # IOU 阈值
        self.declare_parameter("imgsz_height", 640)  # 输入图像高度
        self.declare_parameter("imgsz_width", 640)  # 输入图像宽度
        self.declare_parameter("half", False)  # 是否使用半精度(FP16)
        self.declare_parameter("max_det", 300)  # 最大检测数量
        self.declare_parameter("augment", False)  # 是否使用图像增强
        self.declare_parameter("agnostic_nms", False)  # 是否使用类别无关的 NMS
        self.declare_parameter("retina_masks", False)  # 是否使用 retina mask

        # 模型类型映射
        self.type_to_model = {"YOLO": YOLO, "World": YOLOWorld}

    def on_configure(self, state: LifecycleState) -> TransitionCallbackReturn:
        """
        配置节点状态的回调函数

        获取参数值并初始化发布者等组件

        Args:
            state: 当前生命周期状态

        Returns:
            成功或失败的状态转换结果
        """
        self.get_logger().info(f"[{self.get_name()}] 正在配置...")

        # 获取模型参数
        self.model_type = (
            self.get_parameter("model_type").get_parameter_value().string_value
        )
        self.model = self.get_parameter("model").get_parameter_value().string_value
        self.device = self.get_parameter("device").get_parameter_value().string_value
        self.yolo_encoding = (
            self.get_parameter("yolo_encoding").get_parameter_value().string_value
        )

        # 获取推理参数
        self.threshold = (
            self.get_parameter("threshold").get_parameter_value().double_value
        )
        self.iou = self.get_parameter("iou").get_parameter_value().double_value
        self.imgsz_height = (
            self.get_parameter("imgsz_height").get_parameter_value().integer_value
        )
        self.imgsz_width = (
            self.get_parameter("imgsz_width").get_parameter_value().integer_value
        )
        self.half = self.get_parameter("half").get_parameter_value().bool_value
        self.max_det = self.get_parameter("max_det").get_parameter_value().integer_value
        self.augment = self.get_parameter("augment").get_parameter_value().bool_value
        self.agnostic_nms = (
            self.get_parameter("agnostic_nms").get_parameter_value().bool_value
        )
        self.retina_masks = (
            self.get_parameter("retina_masks").get_parameter_value().bool_value
        )

        # 获取 ROS 参数
        self.enable = self.get_parameter("enable").get_parameter_value().bool_value
        self.reliability = (
            self.get_parameter("image_reliability").get_parameter_value().integer_value
        )

        # 设置 QoS 配置文件
        self.image_qos_profile = QoSProfile(
            reliability=self.reliability,
            history=QoSHistoryPolicy.KEEP_LAST,
            durability=QoSDurabilityPolicy.VOLATILE,
            depth=1,
        )

        # 创建检测结果发布者
        self._pub = self.create_lifecycle_publisher(DetectionArray, "detections", 10)
        self.cv_bridge = CvBridge()

        super().on_configure(state)
        self.get_logger().info(f"[{self.get_name()}] 配置完成")

        return TransitionCallbackReturn.SUCCESS

    def on_activate(self, state: LifecycleState) -> TransitionCallbackReturn:
        """
        激活节点状态的回调函数

        加载 YOLO 模型，创建订阅和服务

        Args:
            state: 当前生命周期状态

        Returns:
            成功或失败的状态转换结果
        """
        self.get_logger().info(f"[{self.get_name()}] 正在激活...")

        try:
            # 加载指定类型的 YOLO 模型
            self.yolo = self.type_to_model[self.model_type](self.model)
        except FileNotFoundError:
            self.get_logger().error(f"模型文件 '{self.model}' 不存在")
            return TransitionCallbackReturn.ERROR

        try:
            # 尝试融合模型以优化推理性能
            self.get_logger().info("尝试融合模型...")
            self.yolo.fuse()
        except TypeError as e:
            self.get_logger().warn(f"融合模型时出错: {e}")

        # 创建启用/禁用服务
        self._enable_srv = self.create_service(SetBool, "enable", self.enable_cb)

        # 如果是 YOLOWorld 模型，创建设置类别的服务
        if isinstance(self.yolo, YOLOWorld):
            self._set_classes_srv = self.create_service(
                SetClasses, "set_classes", self.set_classes_cb
            )

        # 创建图像订阅
        self._sub = self.create_subscription(
            Image, "image_raw", self.image_cb, self.image_qos_profile
        )

        super().on_activate(state)
        self.get_logger().info(f"[{self.get_name()}] 激活完成")

        return TransitionCallbackReturn.SUCCESS

    def on_deactivate(self, state: LifecycleState) -> TransitionCallbackReturn:
        """
        停用节点状态的回调函数

        释放模型资源，销毁订阅和服务

        Args:
            state: 当前生命周期状态

        Returns:
            成功或失败的状态转换结果
        """
        self.get_logger().info(f"[{self.get_name()}] 正在停用...")

        # 删除 YOLO 模型
        del self.yolo
        if "cuda" in self.device:
            self.get_logger().info("正在清理CUDA缓存")
            torch.cuda.empty_cache()

        # 销毁服务和订阅
        self.destroy_service(self._enable_srv)
        self._enable_srv = None

        if isinstance(self.yolo, YOLOWorld):
            self.destroy_service(self._set_classes_srv)
            self._set_classes_srv = None

        self.destroy_subscription(self._sub)
        self._sub = None

        super().on_deactivate(state)
        self.get_logger().info(f"[{self.get_name()}] 停用完成")

        return TransitionCallbackReturn.SUCCESS

    def on_cleanup(self, state: LifecycleState) -> TransitionCallbackReturn:
        """
        清理节点资源的回调函数

        销毁发布者和其他资源

        Args:
            state: 当前生命周期状态

        Returns:
            成功或失败的状态转换结果
        """
        self.get_logger().info(f"[{self.get_name()}] 正在清理...")

        # 销毁发布者
        self.destroy_publisher(self._pub)

        # 删除 QoS 配置
        del self.image_qos_profile

        super().on_cleanup(state)
        self.get_logger().info(f"[{self.get_name()}] 清理完成")

        return TransitionCallbackReturn.SUCCESS

    def on_shutdown(self, state: LifecycleState) -> TransitionCallbackReturn:
        """
        关闭节点的回调函数

        Args:
            state: 当前生命周期状态

        Returns:
            成功或失败的状态转换结果
        """
        self.get_logger().info(f"[{self.get_name()}] 正在关闭...")
        super().on_cleanup(state)
        self.get_logger().info(f"[{self.get_name()}] 关闭完成")
        return TransitionCallbackReturn.SUCCESS

    def enable_cb(
        self,
        request: SetBool.Request,
        response: SetBool.Response,
    ) -> SetBool.Response:
        """
        启用/禁用检测的服务回调函数

        Args:
            request: 包含 data 字段(布尔值)的请求
            response: 服务响应

        Returns:
            设置成功的响应
        """
        self.enable = request.data
        response.success = True
        return response

    def parse_hypothesis(self, results: Results) -> List[Dict]:
        """
        解析检测结果中的假设(类别和置信度)

        Args:
            results: YOLO 检测结果

        Returns:
            包含类别 ID、类别名称和置信度的字典列表
        """
        hypothesis_list = []

        if results.boxes:
            box_data: Boxes
            for box_data in results.boxes:
                hypothesis = {
                    "class_id": int(box_data.cls),
                    "class_name": self.yolo.names[int(box_data.cls)],
                    "score": float(box_data.conf),
                }
                hypothesis_list.append(hypothesis)

        elif results.obb:
            for i in range(results.obb.cls.shape[0]):
                hypothesis = {
                    "class_id": int(results.obb.cls[i]),
                    "class_name": self.yolo.names[int(results.obb.cls[i])],
                    "score": float(results.obb.conf[i]),
                }
                hypothesis_list.append(hypothesis)

        return hypothesis_list

    def parse_boxes(self, results: Results) -> List[BoundingBox2D]:
        """
        解析检测结果中的边界框

        Args:
            results: YOLO 检测结果

        Returns:
            BoundingBox2D 消息列表
        """
        boxes_list = []

        if results.boxes:
            box_data: Boxes
            for box_data in results.boxes:

                msg = BoundingBox2D()

                # 获取边界框值 (中心坐标和尺寸)
                box = box_data.xywh[0]
                msg.center.position.x = float(box[0])
                msg.center.position.y = float(box[1])
                msg.size.x = float(box[2])
                msg.size.y = float(box[3])

                # 添加消息
                boxes_list.append(msg)

        elif results.obb:
            for i in range(results.obb.cls.shape[0]):
                msg = BoundingBox2D()

                # 获取方向边界框值 (中心坐标、尺寸和旋转角度)
                box = results.obb.xywhr[i]
                msg.center.position.x = float(box[0])
                msg.center.position.y = float(box[1])
                msg.center.theta = float(box[4])
                msg.size.x = float(box[2])
                msg.size.y = float(box[3])

                # 添加消息
                boxes_list.append(msg)

        return boxes_list

    def parse_masks(self, results: Results) -> List[Mask]:
        """
        解析检测结果中的掩码

        Args:
            results: YOLO 检测结果

        Returns:
            Mask 消息列表
        """
        masks_list = []

        def create_point2d(x: float, y: float) -> Point2D:
            """创建一个 Point2D 消息"""
            p = Point2D()
            p.x = x
            p.y = y
            return p

        mask: Masks
        for mask in results.masks:

            msg = Mask()

            # 转换掩码轮廓点为 Point2D 消息列表
            msg.data = [
                create_point2d(float(ele[0]), float(ele[1]))
                for ele in mask.xy[0].tolist()
            ]
            msg.height = results.orig_img.shape[0]
            msg.width = results.orig_img.shape[1]

            masks_list.append(msg)

        return masks_list

    def parse_keypoints(self, results: Results) -> List[KeyPoint2DArray]:
        """
        解析检测结果中的关键点

        Args:
            results: YOLO 检测结果

        Returns:
            KeyPoint2DArray 消息列表
        """
        keypoints_list = []

        points: Keypoints
        for points in results.keypoints:

            msg_array = KeyPoint2DArray()

            if points.conf is None:
                continue

            # 遍历所有关键点
            for kp_id, (p, conf) in enumerate(zip(points.xy[0], points.conf[0])):

                # 只添加置信度高于阈值的关键点
                if conf >= self.threshold:
                    msg = KeyPoint2D()

                    msg.id = kp_id + 1
                    msg.point.x = float(p[0])
                    msg.point.y = float(p[1])
                    msg.score = float(conf)

                    msg_array.data.append(msg)

            keypoints_list.append(msg_array)

        return keypoints_list

    def image_cb(self, msg: Image) -> None:
        """
        图像消息回调函数

        处理输入图像并发布检测结果

        Args:
            msg: 输入图像消息
        """
        if self.enable:

            # 转换图像并进行推理
            cv_image = self.cv_bridge.imgmsg_to_cv2(
                msg, desired_encoding=self.yolo_encoding
            )
            results = self.yolo.predict(
                source=cv_image,
                verbose=False,
                stream=False,
                conf=self.threshold,
                iou=self.iou,
                imgsz=(self.imgsz_height, self.imgsz_width),
                half=self.half,
                max_det=self.max_det,
                augment=self.augment,
                agnostic_nms=self.agnostic_nms,
                retina_masks=self.retina_masks,
                device=self.device,
            )
            results: Results = results[0].cpu()

            # 解析边界框和类别信息
            if results.boxes or results.obb:
                hypothesis = self.parse_hypothesis(results)
                boxes = self.parse_boxes(results)

            # 解析掩码信息
            if results.masks:
                masks = self.parse_masks(results)

            # 解析关键点信息
            if results.keypoints:
                keypoints = self.parse_keypoints(results)

            # 创建检测消息数组
            detections_msg = DetectionArray()

            # 为每个检测结果创建 Detection 消息
            for i in range(len(results)):

                aux_msg = Detection()

                # 添加边界框和类别信息
                if results.boxes or results.obb and hypothesis and boxes:
                    aux_msg.class_id = hypothesis[i]["class_id"]
                    aux_msg.class_name = hypothesis[i]["class_name"]
                    aux_msg.score = hypothesis[i]["score"]

                    aux_msg.bbox = boxes[i]

                # 添加掩码信息
                if results.masks and masks:
                    aux_msg.mask = masks[i]

                # 添加关键点信息
                if results.keypoints and keypoints:
                    aux_msg.keypoints = keypoints[i]

                detections_msg.detections.append(aux_msg)

            # 发布检测结果
            detections_msg.header = msg.header
            self._pub.publish(detections_msg)

            # 释放资源
            del results
            del cv_image

    def set_classes_cb(
        self,
        req: SetClasses.Request,
        res: SetClasses.Response,
    ) -> SetClasses.Response:
        """
        设置 YOLOWorld 模型检测类别的服务回调函数

        允许动态更新模型需要检测的目标类别

        Args:
            req: 包含 classes 字段(字符串列表)的请求
            res: 服务响应

        Returns:
            服务响应
        """
        self.get_logger().info(f"设置检测类别: {req.classes}")
        self.yolo.set_classes(req.classes)
        self.get_logger().info(f"新的检测类别: {self.yolo.names}")
        return res


def main():
    """
    主函数

    初始化 ROS 2，创建并配置 YOLO 节点，然后开始运行
    """
    rclpy.init()
    node = YoloNode()
    node.trigger_configure()
    node.trigger_activate()
    rclpy.spin(node)
    node.destroy_node()
    rclpy.shutdown()
