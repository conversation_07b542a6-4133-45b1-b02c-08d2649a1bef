<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>yolo_ros</name>
  <version>4.2.0</version>
  <description>YOLO for ROS 2</description>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <license>GPL-3</license>

  <test_depend>ament_copyright</test_depend>
  <test_depend>ament_flake8</test_depend>
  <test_depend>ament_pep257</test_depend>
  <test_depend>python3-pytest</test_depend>

  <depend>cv_bridge</depend>
  <depend>std_srvs</depend>
  <depend>sensor_msgs</depend>
  <depend>yolo_msgs</depend>

  <export>
    <build_type>ament_python</build_type>
  </export>
</package>
