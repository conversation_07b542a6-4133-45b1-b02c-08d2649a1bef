# People Detection ROS 2包

这是一个用于人员检测的ROS 2软件包，基于YOLO系列目标检测模型，支持2D和3D检测，并集成了追踪功能。

## 功能概述

- 使用YOLO系列模型（YOLOv5/8/9/10/11/12和YOLO-World）进行物体检测
- 支持2D检测（边界框、掩码、关键点）
- 支持3D检测（通过深度相机）
- 集成目标追踪功能
- 提供可视化调试节点

## 包结构

```
people_detection/
├── yolo_msgs/           # 自定义消息定义
│   ├── msg/             # 消息类型定义（边界框、关键点、检测结果等）
│   │   ├── BoundingBox2D.msg    # 2D边界框
│   │   ├── BoundingBox3D.msg    # 3D边界框
│   │   ├── Detection.msg        # 检测结果
│   │   ├── DetectionArray.msg   # 检测结果数组
│   │   ├── KeyPoint2D.msg       # 2D关键点
│   │   ├── KeyPoint3D.msg       # 3D关键点
│   │   └── Mask.msg             # 分割掩码
│   ├── srv/             # 服务类型定义
│   ├── package.xml      # 包信息
│   └── CMakeLists.txt   # 编译配置
├── yolo_ros/            # 核心功能实现
│   ├── yolo_ros/        # 实现各类节点的Python代码
│   │   ├── yolo_node.py         # 基本的2D检测节点
│   │   ├── detect_3d_node.py    # 3D检测节点
│   │   ├── tracking_node.py     # 追踪功能节点
│   │   └── debug_node.py        # 调试和可视化节点
│   ├── package.xml      # 包信息
│   └── setup.py         # Python包配置
└── yolo_bringup/        # 启动文件
    ├── launch/          # 各类配置的启动文件
    │   ├── yolo.launch.py       # 主启动文件
    │   ├── yolo-world.launch.py # YOLO-World启动文件
    │   └── yolov*.launch.py     # 针对不同YOLO版本的启动配置
    ├── package.xml      # 包信息
    └── CMakeLists.txt   # 编译配置
```

## 消息类型

本软件包定义了多种自定义消息类型：

- `BoundingBox2D/3D`: 2D和3D边界框
- `Detection`: 检测结果，包含边界框、置信度、类别、ID等信息
- `DetectionArray`: 多个检测结果的数组
- `KeyPoint2D/3D`: 2D和3D关键点（用于人体姿态估计）
- `KeyPoint2DArray/3DArray`: 关键点数组
- `Mask`: 分割掩码
- `Point2D`/`Pose2D`: 2D点和姿态

## 主要节点

1. **YoloNode**: 基础2D检测节点
   - 负责运行YOLO模型并发布检测结果
   - 支持不同的YOLO模型（v5/8/9/10/11/12和YOLO-World）
   - 支持动态启用/禁用检测

2. **Detect3DNode**: 3D检测节点
   - 将2D检测结果结合深度信息转换为3D数据
   - 支持坐标系转换（通过TF）
   - 处理2D边界框和关键点转3D

3. **TrackingNode**: 追踪节点
   - 基于检测结果进行目标追踪
   - 支持多种追踪算法（如ByteTrack）
   - 为检测对象分配唯一ID

4. **DebugNode**: 调试节点
   - 提供检测结果的可视化
   - 显示边界框、关键点和掩码

## 工作流程

1. `YoloNode`接收图像数据，运行YOLO模型进行检测，发布2D检测结果
2. 若启用3D检测，`Detect3DNode`接收2D检测结果和深度图像，生成3D检测结果
3. 若启用追踪，`TrackingNode`接收检测结果，进行目标追踪并添加ID
4. `DebugNode`可选择性地提供可视化输出

## 依赖

- ROS 2
- PyTorch
- Ultralytics YOLO (v8.3.91)
- OpenCV (>=********)
- NumPy
- LAP (线性分配问题求解器，用于追踪)

## 使用方法(如果如果项目已经安装，不需要重新运行了！)

### 安装

1. 安装所需依赖：
   ```bash
   pip install -r requirements.txt
   ```

2. 将此包添加到你的ROS 2工作空间并编译：
   ```bash
   cd ~/your_workspace/src
   git clone <repository_url> people_detection
   cd ..
   colcon build --packages-select yolo_msgs yolo_ros yolo_bringup
   ```

### 运行

使用启动文件运行基本检测：
```bash
ros2 launch yolo_bringup yolo.launch.py
```

对于特定版本的YOLO模型(需要指定订阅的相机话题，默认话题为`/camera/rgb/image_raw`)：
```bash
ros2 launch yolo_bringup yolov8.launch.py input_image_topic:=/camera/color/image_raw
```

### 配置参数

启动文件支持多种参数配置：
- `model_type`: 模型类型（YOLO或World）
- `model`: YOLO模型文件或名称
- `device`: 运行设备（CUDA/CPU）
- `threshold`: 检测阈值
- `input_image_topic`: 输入图像话题
- `input_depth_topic`: 输入深度图像话题（3D检测）
- `target_frame`: 3D转换的目标坐标系
- `use_tracking`: 是否启用追踪功能
- `use_3d`: 是否启用3D检测

## 示例

基本使用示例：
```bash
# 运行带有追踪功能的YOLOv8检测
ros2 launch yolo_bringup yolov8.launch.py use_tracking:=True

# 运行带有3D检测的YOLOv8 (需要深度信息)
ros2 launch yolo_bringup yolov8.launch.py use_3d:=True

# 使用YOLO-World模型
ros2 launch yolo_bringup yolo-world.launch.py
```
注：注意一些参数的设置


## 服务

包提供以下服务：
- 启用/禁用检测（`SetBool`服务）
- 设置检测类别（对于YOLO-World模型）