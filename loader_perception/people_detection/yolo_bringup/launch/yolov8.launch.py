import os
from launch import LaunchDescription
from launch.substitutions import LaunchConfiguration
from launch.actions import IncludeLaunchDescription
from launch.launch_description_sources import PythonLaunchDescriptionSource
from ament_index_python.packages import get_package_share_directory


def generate_launch_description():
    """
    生成YOLOv8启动描述
    此函数配置并返回YOLOv8目标检测节点的启动描述

    当执行这个launch文件时，执行过程如下：

    1. ROS 2的launch系统调用`generate_launch_description()`函数
    2. 该函数返回一个`LaunchDescription`对象，其中包含一个`IncludeLaunchDescription`动作
    3. `IncludeLaunchDescription`实际上是引用了另一个launch文件：`yolo.launch.py`
    4. 系统从`yolo_bringup`包的共享目录中查找并加载这个被引用的launch文件
    5. 同时传递一系列参数给`yolo.launch.py`，包括：
    - model：使用的YOLO模型，默认为"yolov8m.pt"
    - tracker：目标跟踪器配置文件，默认为"bytetrack.yaml"
    - device：运行设备，默认为"cuda:0"（GPU）
    - threshold：检测阈值，默认为0.5
    - input_image_topic：输入图像话题，默认为"/camera/rgb/image_raw"
    - 其他配置参数如image_reliability、namespace等

    """

    return LaunchDescription(
        [
            IncludeLaunchDescription(
                PythonLaunchDescriptionSource(
                    os.path.join(
                        get_package_share_directory("yolo_bringup"),
                        "launch",
                        "yolo.launch.py",
                    )
                ),
                launch_arguments={
                    # YOLO模型文件，默认使用yolov8m.pt
                    "model": LaunchConfiguration("model", default="yolov8m.pt"),
                    # 目标跟踪器配置文件
                    "tracker": LaunchConfiguration("tracker", default="bytetrack.yaml"),
                    # 运行设备，默认使用CUDA
                    "device": LaunchConfiguration("device", default="cuda:0"),
                    # 是否启用节点
                    "enable": LaunchConfiguration("enable", default="True"),
                    # 检测阈值，低于此值的检测结果将被忽略
                    "threshold": LaunchConfiguration("threshold", default="0.5"),
                    # 输入图像话题
                    "input_image_topic": LaunchConfiguration(
                        "input_image_topic", default="/camera/rgb/image_raw"
                    ),
                    # 图像可靠性参数
                    "image_reliability": LaunchConfiguration(
                        "image_reliability", default="1"
                    ),
                    # 节点命名空间
                    "namespace": LaunchConfiguration("namespace", default="yolo"),
                }.items(),
            )
        ]
    )
