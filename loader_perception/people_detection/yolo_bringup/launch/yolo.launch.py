# Import necessary modules from the ROS 2 launch system
from launch import LaunchDescription, LaunchContext
from launch.actions import DeclareLaunchArgument, OpaqueFunction
from launch.substitutions import LaunchConfiguration, PythonExpression
from launch_ros.actions import Node
from launch.conditions import IfCondition


# Function to generate the launch description for the YOLO ROS 2 nodes
def generate_launch_description():

    # Nested function to configure and run YOLO nodes based on launch arguments
    def run_yolo(context: LaunchContext, use_tracking, use_3d):
        # Evaluate launch configuration values within the context
        use_tracking = eval(context.perform_substitution(use_tracking))
        use_3d = eval(context.perform_substitution(use_3d))

        # --- Launch Arguments ---
        model_type = LaunchConfiguration("model_type")
        model_type_cmd = DeclareLaunchArgument(
            "model_type",
            default_value="YOLO",
            choices=["YOLO", "World"],
            description="Model type form Ultralytics (YOLO, World)",
        )

        model = LaunchConfiguration("model")
        model_cmd = DeclareLaunchArgument(
            "model",
            default_value="yolov8m.pt",
            description="Model name or path (e.g., yolov8m.pt, yolov8m-seg.pt)",
        )

        tracker = LaunchConfiguration("tracker")
        tracker_cmd = DeclareLaunchArgument(
            "tracker",
            default_value="bytetrack.yaml",
            description="Tracker configuration file name or path (e.g., bytetrack.yaml)",
        )

        device = LaunchConfiguration("device")
        device_cmd = DeclareLaunchArgument(
            "device",
            default_value="cuda:0",
            description="Device to use for computation (e.g., 'cuda:0' for GPU, 'cpu' for CPU)",
        )

        yolo_encoding = LaunchConfiguration("yolo_encoding")
        yolo_encoding_cmd = DeclareLaunchArgument(
            "yolo_encoding",
            default_value="bgr8",
            description="Encoding of the input image topic",
        )

        enable = LaunchConfiguration("enable")
        enable_cmd = DeclareLaunchArgument(
            "enable",
            default_value="True",
            description="Whether to start YOLO enabled",
        )

        threshold = LaunchConfiguration("threshold")
        threshold_cmd = DeclareLaunchArgument(
            "threshold",
            default_value="0.5",
            description="Minimum confidence threshold for detections to be published",
        )

        iou = LaunchConfiguration("iou")
        iou_cmd = DeclareLaunchArgument(
            "iou",
            default_value="0.7",
            description="Intersection over Union (IoU) threshold for Non-Maximum Suppression (NMS)",
        )

        imgsz_height = LaunchConfiguration("imgsz_height")
        imgsz_height_cmd = DeclareLaunchArgument(
            "imgsz_height",
            default_value="480",
            description="Image height used for inference",
        )

        imgsz_width = LaunchConfiguration("imgsz_width")
        imgsz_width_cmd = DeclareLaunchArgument(
            "imgsz_width",
            default_value="640",
            description="Image width used for inference",
        )

        half = LaunchConfiguration("half")
        half_cmd = DeclareLaunchArgument(
            "half",
            default_value="False",
            description="Enable half-precision (FP16) inference for faster processing",
        )

        max_det = LaunchConfiguration("max_det")
        max_det_cmd = DeclareLaunchArgument(
            "max_det",
            default_value="300",
            description="Maximum number of detections allowed per image",
        )

        augment = LaunchConfiguration("augment")
        augment_cmd = DeclareLaunchArgument(
            "augment",
            default_value="False",
            description="Enable test-time augmentation (TTA) for improved detection robustness",
        )

        agnostic_nms = LaunchConfiguration("agnostic_nms")
        agnostic_nms_cmd = DeclareLaunchArgument(
            "agnostic_nms",
            default_value="False",
            description="Enable class-agnostic NMS, merging boxes of different classes",
        )

        retina_masks = LaunchConfiguration("retina_masks")
        retina_masks_cmd = DeclareLaunchArgument(
            "retina_masks",
            default_value="False",
            description="Use high-resolution segmentation masks if available",
        )

        # --- Topic Configurations ---
        input_image_topic = LaunchConfiguration("input_image_topic")
        input_image_topic_cmd = DeclareLaunchArgument(
            "input_image_topic",
            default_value="/camera/rgb/image_raw",
            description="Name of the input image topic for YOLO",
        )

        image_reliability = LaunchConfiguration("image_reliability")
        image_reliability_cmd = DeclareLaunchArgument(
            "image_reliability",
            default_value="1",
            choices=["0", "1", "2"],
            description="QoS reliability for the input image topic (0: system default, 1: Reliable, 2: Best Effort)",
        )

        # --- Depth-related configurations (used if use_3d is True) ---
        input_depth_topic = LaunchConfiguration("input_depth_topic")
        input_depth_topic_cmd = DeclareLaunchArgument(
            "input_depth_topic",
            default_value="/camera/depth/image_raw",
            description="Name of the input depth image topic",
        )

        depth_image_reliability = LaunchConfiguration("depth_image_reliability")
        depth_image_reliability_cmd = DeclareLaunchArgument(
            "depth_image_reliability",
            default_value="1",
            choices=["0", "1", "2"],
            description="QoS reliability for the input depth image topic (0: system default, 1: Reliable, 2: Best Effort)",
        )

        input_depth_info_topic = LaunchConfiguration("input_depth_info_topic")
        input_depth_info_topic_cmd = DeclareLaunchArgument(
            "input_depth_info_topic",
            default_value="/camera/depth/camera_info",
            description="Name of the input depth camera info topic",
        )

        depth_info_reliability = LaunchConfiguration("depth_info_reliability")
        depth_info_reliability_cmd = DeclareLaunchArgument(
            "depth_info_reliability",
            default_value="1",
            choices=["0", "1", "2"],
            description="QoS reliability for the input depth info topic (0: system default, 1: Reliable, 2: Best Effort)",
        )

        target_frame = LaunchConfiguration("target_frame")
        target_frame_cmd = DeclareLaunchArgument(
            "target_frame",
            default_value="base_link",
            description="Target frame for transforming 3D bounding boxes",
        )

        depth_image_units_divisor = LaunchConfiguration("depth_image_units_divisor")
        depth_image_units_divisor_cmd = DeclareLaunchArgument(
            "depth_image_units_divisor",
            default_value="1000",
            description="Divisor to convert raw depth image values to meters (e.g., 1000 for mm)",
        )

        maximum_detection_threshold = LaunchConfiguration("maximum_detection_threshold")
        maximum_detection_threshold_cmd = DeclareLaunchArgument(
            "maximum_detection_threshold",
            default_value="0.3",
            description="Maximum detection distance threshold along the Z-axis in meters",
        )

        # --- General Configurations ---
        namespace = LaunchConfiguration("namespace")
        namespace_cmd = DeclareLaunchArgument(
            "namespace",
            default_value="yolo",
            description="Namespace for the YOLO nodes",
        )

        use_debug = LaunchConfiguration("use_debug")
        use_debug_cmd = DeclareLaunchArgument(
            "use_debug",
            default_value="True",
            description="Whether to launch the debug node for visualization",
        )

        # --- Dynamic Topic Remapping Logic ---
        # Determine the input topic for the detect_3d_node based on whether tracking is enabled.
        detect_3d_detections_topic = "detections"
        # Determine the input topic for the debug_node based on whether tracking and/or 3D detection are enabled.
        debug_detections_topic = "detections"

        if use_tracking:
            # If tracking is used, detect_3d should subscribe to the tracking output.
            detect_3d_detections_topic = "tracking"

        if use_tracking and not use_3d:
            # If only tracking is used, debug node subscribes to tracking output.
            debug_detections_topic = "tracking"
        elif use_3d:
            # If 3D detection is used (regardless of tracking), debug node subscribes to 3D detections.
            debug_detections_topic = "detections_3d"

        # --- Node Definitions ---

        # YOLO inference node
        yolo_node_cmd = Node(
            package="yolo_ros",
            executable="yolo_node",
            name="yolo_node",
            namespace=namespace,
            parameters=[
                {
                    "model_type": model_type,
                    "model": model,
                    "device": device,
                    "yolo_encoding": yolo_encoding,
                    "enable": enable,
                    "threshold": threshold,
                    "iou": iou,
                    "imgsz_height": imgsz_height,
                    "imgsz_width": imgsz_width,
                    "half": half,
                    "max_det": max_det,
                    "augment": augment,
                    "agnostic_nms": agnostic_nms,
                    "retina_masks": retina_masks,
                    "image_reliability": image_reliability,
                }
            ],
            remappings=[("image_raw", input_image_topic)], # Remap the generic 'image_raw' input to the specified topic
        )

        # Object tracking node (conditionally launched)
        tracking_node_cmd = Node(
            package="yolo_ros",
            executable="tracking_node",
            name="tracking_node",
            namespace=namespace,
            parameters=[{"tracker": tracker, "image_reliability": image_reliability}],
            remappings=[("image_raw", input_image_topic)], # Also subscribes to the raw image
            condition=IfCondition(PythonExpression([str(use_tracking)])), # Launch only if 'use_tracking' is true
        )

        # 3D detection node (conditionally launched)
        detect_3d_node_cmd = Node(
            package="yolo_ros",
            executable="detect_3d_node",
            name="detect_3d_node",
            namespace=namespace,
            parameters=[
                {
                    "target_frame": target_frame,
                    "maximum_detection_threshold": maximum_detection_threshold,
                    "depth_image_units_divisor": depth_image_units_divisor,
                    "depth_image_reliability": depth_image_reliability,
                    "depth_info_reliability": depth_info_reliability,
                }
            ],
            remappings=[
                ("depth_image", input_depth_topic),
                ("depth_info", input_depth_info_topic),
                ("detections", detect_3d_detections_topic), # Input topic determined by the logic above
            ],
            condition=IfCondition(PythonExpression([str(use_3d)])), # Launch only if 'use_3d' is true
        )

        # Debug visualization node (conditionally launched)
        debug_node_cmd = Node(
            package="yolo_ros",
            executable="debug_node",
            name="debug_node",
            namespace=namespace,
            parameters=[{"image_reliability": image_reliability}],
            remappings=[
                ("image_raw", input_image_topic), # Subscribes to the raw image
                ("detections", debug_detections_topic), # Input topic determined by the logic above
            ],
            condition=IfCondition(PythonExpression([use_debug])), # Launch only if 'use_debug' is true
        )

        # Return the list of all declared arguments and node actions
        return [
            model_type_cmd,
            model_cmd,
            tracker_cmd,
            device_cmd,
            yolo_encoding_cmd,
            enable_cmd,
            threshold_cmd,
            iou_cmd,
            imgsz_height_cmd,
            imgsz_width_cmd,
            half_cmd,
            max_det_cmd,
            augment_cmd,
            agnostic_nms_cmd,
            retina_masks_cmd,
            input_image_topic_cmd,
            image_reliability_cmd,
            input_depth_topic_cmd,
            depth_image_reliability_cmd,
            input_depth_info_topic_cmd,
            depth_info_reliability_cmd,
            target_frame_cmd,
            depth_image_units_divisor_cmd,
            maximum_detection_threshold_cmd,
            namespace_cmd,
            use_debug_cmd,
            yolo_node_cmd,
            tracking_node_cmd,
            detect_3d_node_cmd,
            debug_node_cmd,
        ]

    # --- Top-level Launch Arguments ---
    # These arguments control the conditional launching of tracking and 3D nodes.
    use_tracking = LaunchConfiguration("use_tracking")
    use_tracking_cmd = DeclareLaunchArgument(
        "use_tracking",
        default_value="True",
        description="Whether to activate object tracking",
    )

    use_3d = LaunchConfiguration("use_3d")
    use_3d_cmd = DeclareLaunchArgument(
        "use_3d",
        default_value="False",
        description="Whether to activate 3D detection using depth information",
    )

    # Create the final LaunchDescription
    return LaunchDescription(
        [
            # Declare the top-level arguments first
            use_tracking_cmd,
            use_3d_cmd,
            # Use OpaqueFunction to allow accessing launch configuration values
            # within the function that defines the nodes. This is necessary because
            # the node definitions depend on the values of 'use_tracking' and 'use_3d'.
            OpaqueFunction(function=run_yolo, args=[use_tracking, use_3d]),
        ]
    )
