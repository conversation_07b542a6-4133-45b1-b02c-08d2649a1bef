#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration
from launch_ros.actions import Node


def generate_launch_description():
    target_frame_arg = DeclareLaunchArgument(
        'target_frame',
        default_value='base_link',
        description='目标坐标系'
    )
    
    camera_frame_arg = DeclareLaunchArgument(
        'camera_frame',
        default_value='camera',
        description='相机坐标系'
    )
    
    lidar_frame_arg = DeclareLaunchArgument(
        'lidar_frame',
        default_value='rslidar',
        description='激光雷达坐标系'
    )
    
    min_points_in_box_arg = DeclareLaunchArgument(
        'min_points_in_box',
        default_value='10',
        description='边界框内最小点云数量'
    )
    
    distance_threshold_arg = DeclareLaunchArgument(
        'distance_threshold',
        default_value='0.05',
        description='欧式聚类距离阈值'
    )
    
    min_cluster_size_arg = DeclareLaunchArgument(
        'min_cluster_size',
        default_value='5',
        description='最小聚类点数'
    )
    
    max_cluster_size_arg = DeclareLaunchArgument(
        'max_cluster_size',
        default_value='10000',
        description='最大聚类点数'
    )
    
    visualize_arg = DeclareLaunchArgument(
        'visualize',
        default_value='true',
        description='是否可视化'
    )
    
    debug_arg = DeclareLaunchArgument(
        'debug',
        default_value='true',
        description='是否开启调试模式'
    )

    use_manual_transform_arg = DeclareLaunchArgument(
        'use_manual_transform',
        default_value='true',
        description='是否使用手动变换矩阵'
    )

    human_fusion_3d_node = Node(
        package='human_fusion_3d',
        executable='human_fusion_3d_node',
        name='human_fusion_3d',
        output='screen',
        parameters=[{
            'target_frame': LaunchConfiguration('target_frame'),
            'camera_frame': LaunchConfiguration('camera_frame'),
            'lidar_frame': LaunchConfiguration('lidar_frame'),
            'min_points_in_box': LaunchConfiguration('min_points_in_box'),
            'distance_threshold': LaunchConfiguration('distance_threshold'),
            'min_cluster_size': LaunchConfiguration('min_cluster_size'),
            'max_cluster_size': LaunchConfiguration('max_cluster_size'),
            'visualize': LaunchConfiguration('visualize'),
            'debug': LaunchConfiguration('debug'),
            'use_manual_transform': LaunchConfiguration('use_manual_transform'),
        }]
    )
    
    return LaunchDescription([
        target_frame_arg,
        camera_frame_arg,
        lidar_frame_arg,
        min_points_in_box_arg,
        distance_threshold_arg,
        min_cluster_size_arg,
        max_cluster_size_arg,
        visualize_arg,
        debug_arg,
        use_manual_transform_arg,
        human_fusion_3d_node,
    ]) 