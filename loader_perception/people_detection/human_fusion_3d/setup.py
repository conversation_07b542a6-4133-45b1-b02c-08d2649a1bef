from setuptools import setup

package_name = 'human_fusion_3d'

setup(
    name=package_name,
    version='0.0.0',
    packages=[package_name],
    data_files=[
        ('share/ament_index/resource_index/packages',
            ['resource/' + package_name]),
        ('share/' + package_name, ['package.xml']),
        ('share/' + package_name + '/launch', ['launch/human_fusion_3d.launch.py']),
    ],
    install_requires=['setuptools'],
    zip_safe=True,
    maintainer='root',
    maintainer_email='<EMAIL>',
    description='基于2D检测和点云数据的3D人体检测功能包',
    license='Apache License 2.0',
    entry_points={
        'console_scripts': [
            'human_fusion_3d_node = human_fusion_3d.human_fusion_3d_node:main',
        ],
    },
)
