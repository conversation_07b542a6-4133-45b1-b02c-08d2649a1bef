# 人体3D融合检测节点

该节点基于2D检测结果与点云数据进行3D人体检测。节点通过融合相机图像中的人体检测结果与点云数据，计算出人体的3D位置并发布。

## 功能特点

- 接收并同步相机图像、相机参数、YOLO检测结果和点云数据
- 将点云从雷达坐标系转换到相机坐标系
- 将点云投影到图像平面
- 提取2D人体检测框内的点云
- 计算人体3D位置
- 动态估计人体尺寸和姿态
- 发布人体位置和可视化标记
- 提供图像可视化结果，包括3D包围盒投影

## 坐标变换说明

节点支持两种坐标系变换方式：

1. **TF树变换**：通过ROS2的TF树获取从激光雷达到相机的坐标变换。
2. **手动变换矩阵（默认）**：使用预设的变换矩阵进行坐标转换，无需依赖TF树。

注：这里手动指的就是自己标定的变换矩阵，而在传感器节点或者录制的bag文件中没有雷达到相机或者`base_link`的变换而手动发布的。

### 如何切换变换方式

在节点启动参数中设置`use_manual_transform`参数：

```bash
ros2 run human_fusion_3d human_fusion_3d_node --ros-args -p use_manual_transform:=false
```

或在启动文件中：

```xml
<node pkg="human_fusion_3d" exec="human_fusion_3d_node" name="human_fusion_3d">
  <param name="use_manual_transform" value="true"/>
</node>
```

### 如何修改手动变换矩阵

如果您需要修改手动变换矩阵，请编辑`human_fusion_3d_node.py`文件中的`on_configure`函数，找到如下代码：

```python
# 设置手动变换矩阵（雷达到相机的变换）
self.manual_transform_matrix = np.array([
    [0.0065, -0.9999, 0.0139, 0.0375],
    [-0.0045, -0.0139, -0.9999, -0.1166],
    [1.0000, 0.0065, -0.0046, -0.1435],
    [0.0000, 0.0000, 0.0000, 1.0000]
])
```

将矩阵值替换为您的雷达到相机的变换矩阵。您可以通过以下方法获取变换矩阵：

1. 使用标定工具计算变换矩阵
2. 从TF树中导出变换矩阵：
   ```bash
   ros2 run tf2_ros tf2_echo <lidar_frame> <camera_frame>
   ```
3. 使用校准验证工具验证变换矩阵的准确性

## 启动方式
### 节点运行

```bash
ros2 run human_fusion_3d human_fusion_3d_node
```
### launch运行

```bash
ros2 launch human_fusion_3d human_fusion_3d.launch.py
```
或者debug模式运行
```bash
ros2 launch human_fusion_3d human_fusion_3d.launch.py --debug 2>&1 | grep -v "\[DEBUG\] \[launch"
```

## 主要参数

| 参数名 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| target_frame | string | base_link | 输出结果的坐标系 |
| camera_frame | string | camera_color_optical_frame | 相机坐标系 |
| lidar_frame | string | rslidar | 激光雷达坐标系 |
| min_points_in_box | int | 10 | 检测框中最少点云数量 |
| distance_threshold | float | 0.05 | 欧式聚类距离阈值 |
| min_cluster_size | int | 5 | 最小聚类点数 |
| max_cluster_size | int | 10000 | 最大聚类点数 |
| visualize | bool | true | 是否可视化 |
| debug | bool | true | 是否打印详细调试信息 |
| use_manual_transform | bool | true | 是否使用手动变换矩阵 |
| bbox_line_thickness | int | 2 | 3D包围盒线条粗细 |
| bbox_color_r | int | 0 | 3D包围盒颜色R通道(0-255) |
| bbox_color_g | int | 255 | 3D包围盒颜色G通道(0-255) |
| bbox_color_b | int | 0 | 3D包围盒颜色B通道(0-255) |

## 订阅话题

- `/camera/color/image_raw` (sensor_msgs/Image): 相机图像
- `/camera/color/camera_info` (sensor_msgs/CameraInfo): 相机参数
- `/yolo/detections` (yolo_msgs/DetectionArray): YOLO检测结果
- `/rslidar_points` (sensor_msgs/PointCloud2): 激光雷达点云
- 变换手动发布

## 发布话题

- `~/human_detections` (geometry_msgs/PoseArray): 人体3D位置
- `~/visualization_marker` (visualization_msgs/MarkerArray): 可视化标记
- `~/visualization_image` (sensor_msgs/Image): 带有3D包围盒投影的可视化图像