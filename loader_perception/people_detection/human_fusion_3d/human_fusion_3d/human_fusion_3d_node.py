#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import numpy as np
import cv2
import rclpy
from rclpy.lifecycle import LifecycleNode, State, TransitionCallbackReturn
from rclpy.node import Node
from rclpy.time import Time
from rclpy.duration import Duration
from rclpy.qos import QoSProfile, ReliabilityPolicy, HistoryPolicy
from rclpy.callback_groups import MutuallyExclusiveCallbackGroup

from sensor_msgs.msg import Image, CameraInfo, PointCloud2
import sensor_msgs_py.point_cloud2 as pc2
from geometry_msgs.msg import PoseArray, Pose, Point, Vector3
from visualization_msgs.msg import Marker, MarkerArray
from std_msgs.msg import ColorRGBA, Header
from geometry_msgs.msg import TransformStamped
from yolo_msgs.msg import DetectionArray

import message_filters
import tf2_ros
from tf2_geometry_msgs import do_transform_point
import tf2_py

import cv_bridge
import time

class HumanFusion3DNode(LifecycleNode):
    """
    人体3D检测节点
    基于2D检测结果与点云数据进行3D检测
    """

    def __init__(self):
        """初始化节点，声明所有必要的参数"""
        super().__init__('human_fusion_3d_node')

        # 参数声明
        self.declare_parameter('target_frame', 'base_link')
        self.declare_parameter('camera_frame', 'camera_color_optical_frame')
        self.declare_parameter('lidar_frame', 'rslidar')
        self.declare_parameter('min_points_in_box', 10)
        self.declare_parameter('distance_threshold', 0.05)  # 欧式聚类距离阈值
        self.declare_parameter('min_cluster_size', 5)  # 最小聚类点数
        self.declare_parameter('max_cluster_size', 10000)  # 最大聚类点数
        self.declare_parameter('visualize', True)  # 是否可视化
        self.declare_parameter('debug', True)  # 是否打印详细调试信息
        self.declare_parameter('use_manual_transform', True)  # 是否使用手动变换矩阵而非TF查询
        self.declare_parameter('bbox_line_thickness', 2)  # 3D包围盒线条粗细
        self.declare_parameter('bbox_color_r', 0)  # 3D包围盒颜色R通道(0-255)
        self.declare_parameter('bbox_color_g', 255)  # 3D包围盒颜色G通道(0-255)
        self.declare_parameter('bbox_color_b', 0)  # 3D包围盒颜色B通道(0-255)

        # 初始化类变量
        # 用于坐标变换
        self._tf_buffer = None
        self._tf_listener = None

        # 用于图像转换
        self._cv_bridge = None

        # 订阅者与发布者
        self._camera_sub = None
        self._camera_info_sub = None  # 这将变为普通订阅者，而非同步订阅者
        self._yolo_sub = None
        self._lidar_sub = None
        self._sync = None

        self._detection_pub = None
        self._marker_pub = None
        self._visualization_img_pub = None  # 添加图像可视化发布者

        # 相机内参
        self._camera_matrix = None
        self._dist_coeffs = None
        self._camera_info_received = False  # 新增标志，表示是否已接收相机信息

        # 手动变换矩阵
        self._manual_transform_matrix = None

        # 调试计数器
        self._frame_count = 0
        self._last_log_time = time.time()

        # 参数值
        self._target_frame = None
        self._camera_frame = None
        self._lidar_frame = None
        self._min_points_in_box = None
        self._distance_threshold = None
        self._min_cluster_size = None
        self._max_cluster_size = None
        self._visualize = None
        self._debug = None
        self._use_manual_transform = None
        self._bbox_line_thickness = None
        self._bbox_color_r = None
        self._bbox_color_g = None
        self._bbox_color_b = None

    def on_configure(self, state: State) -> TransitionCallbackReturn:
        """配置回调函数：获取参数，创建发布者"""
        config_start_time = time.time()
        self.get_logger().info('配置节点...')

        try:
            # 获取参数
            self._target_frame = self.get_parameter('target_frame').value
            self._camera_frame = self.get_parameter('camera_frame').value
            self._lidar_frame = self.get_parameter('lidar_frame').value
            self._min_points_in_box = self.get_parameter('min_points_in_box').value
            self._distance_threshold = self.get_parameter('distance_threshold').value
            self._min_cluster_size = self.get_parameter('min_cluster_size').value
            self._max_cluster_size = self.get_parameter('max_cluster_size').value
            self._visualize = self.get_parameter('visualize').value
            self._debug = self.get_parameter('debug').value
            self._use_manual_transform = self.get_parameter('use_manual_transform').value
            self._bbox_line_thickness = self.get_parameter('bbox_line_thickness').value
            self._bbox_color_r = self.get_parameter('bbox_color_r').value
            self._bbox_color_g = self.get_parameter('bbox_color_g').value
            self._bbox_color_b = self.get_parameter('bbox_color_b').value

            self.get_logger().info(f'参数加载完成: target_frame={self._target_frame}, '
                                f'camera_frame={self._camera_frame}, lidar_frame={self._lidar_frame}')

            # 设置手动变换矩阵（雷达到相机的变换）
            self._manual_transform_matrix = np.array([
                [0.0065, -0.9999, 0.0139, 0.0375],
                [-0.0045, -0.0139, -0.9999, -0.1166],
                [1.0000, 0.0065, -0.0046, -0.1435],
                [0.0000, 0.0000, 0.0000, 1.0000]
            ])
            self.get_logger().info('手动变换矩阵已设置')

            # 初始化CV桥接器（可重用资源）
            self._cv_bridge = cv_bridge.CvBridge()
            self.get_logger().info('CV桥接器初始化完成')

            # 定义QoS
            qos = QoSProfile(
                reliability=ReliabilityPolicy.RELIABLE,
                history=HistoryPolicy.KEEP_LAST,
                depth=5
            )

            # 初始化生命周期发布者
            self._detection_pub = self.create_lifecycle_publisher(
                PoseArray,
                '~/human_detections',
                qos
            )
            self.get_logger().info(f'创建发布者: {self._detection_pub.topic_name}')

            self._marker_pub = self.create_lifecycle_publisher(
                MarkerArray,
                '~/visualization_marker',
                qos
            )
            self.get_logger().info(f'创建发布者: {self._marker_pub.topic_name}')

            # 创建图像可视化发布者
            self._visualization_img_pub = self.create_lifecycle_publisher(
                Image,
                '~/visualization_image',
                qos
            )
            self.get_logger().info(f'创建可视化图像发布者: {self._visualization_img_pub.topic_name}')

            config_end_time = time.time()
            self.get_logger().info(f'节点配置完成，耗时: {config_end_time - config_start_time:.4f}秒')
            return TransitionCallbackReturn.SUCCESS

        except Exception as e:
            self.get_logger().error(f'节点配置失败: {str(e)}')
            return TransitionCallbackReturn.FAILURE

    def on_activate(self, state: State) -> TransitionCallbackReturn:
        """激活回调函数：激活发布者，创建订阅者和服务"""
        activate_start_time = time.time()
        self.get_logger().info('激活节点...')

        try:
            # 激活生命周期发布者
            self._detection_pub.on_activate(state)
            self._marker_pub.on_activate(state)
            self._visualization_img_pub.on_activate(state)  # 激活图像发布者
            self.get_logger().info('发布者已激活')

            # 初始化TF监听器（仅在不使用手动变换时）
            if not self._use_manual_transform:
                self._tf_buffer = tf2_ros.Buffer()
                self._tf_listener = tf2_ros.TransformListener(self._tf_buffer, self)
                self.get_logger().info('TF监听器初始化完成')

            # 创建相机信息的普通订阅者（非同步）
            self._camera_info_sub = self.create_subscription(
                CameraInfo,
                '/camera/color/camera_info',
                self.camera_info_callback,
                10
            )
            self.get_logger().info('订阅话题: /camera/color/camera_info')

            # 初始化其他同步订阅者
            self._camera_sub = message_filters.Subscriber(
                self,
                Image,
                '/camera/color/image_raw'
            )
            self.get_logger().info('订阅话题: /camera/color/image_raw')

            self._yolo_sub = message_filters.Subscriber(
                self,
                DetectionArray,
                '/yolo/detections'
            )
            self.get_logger().info('订阅话题: /yolo/detections')

            self._lidar_sub = message_filters.Subscriber(
                self,
                PointCloud2,
                '/rslidar_points'
            )
            self.get_logger().info('订阅话题: /rslidar_points')

            # 实例化同步器 - 不再包含相机信息
            self._sync = message_filters.ApproximateTimeSynchronizer(
                [self._camera_sub, self._yolo_sub, self._lidar_sub],
                queue_size=10,
                slop=0.1
            )
            self._sync.registerCallback(self.sync_callback)
            self.get_logger().info('消息同步器初始化完成，slop=0.1')

            activate_end_time = time.time()
            self.get_logger().info(f'节点已激活，耗时: {activate_end_time - activate_start_time:.4f}秒')
            return TransitionCallbackReturn.SUCCESS

        except Exception as e:
            self.get_logger().error(f'激活节点失败: {str(e)}')
            return TransitionCallbackReturn.FAILURE

    def on_deactivate(self, state: State) -> TransitionCallbackReturn:
        """去激活回调函数：去激活发布者，清理订阅者"""
        self.get_logger().info('去激活节点...')

        try:
            # 去激活生命周期发布者
            if self._detection_pub is not None:
                self._detection_pub.on_deactivate(state)

            if self._marker_pub is not None:
                self._marker_pub.on_deactivate(state)
                
            if self._visualization_img_pub is not None:
                self._visualization_img_pub.on_deactivate(state)

            # 清理订阅者
            self._camera_sub = None
            self._camera_info_sub = None
            self._yolo_sub = None
            self._lidar_sub = None
            self._sync = None

            # 清理TF监听器
            if not self._use_manual_transform:
                self._tf_buffer = None
                self._tf_listener = None

            self.get_logger().info('节点已去激活')
            return TransitionCallbackReturn.SUCCESS

        except Exception as e:
            self.get_logger().error(f'去激活节点失败: {str(e)}')
            return TransitionCallbackReturn.FAILURE

    def on_cleanup(self, state: State) -> TransitionCallbackReturn:
        """清理回调函数：清理所有资源"""
        self.get_logger().info('清理节点...')

        try:
            # 销毁发布者
            if self._detection_pub is not None:
                self.destroy_lifecycle_publisher(self._detection_pub)
                self._detection_pub = None

            if self._marker_pub is not None:
                self.destroy_lifecycle_publisher(self._marker_pub)
                self._marker_pub = None
                
            if self._visualization_img_pub is not None:
                self.destroy_lifecycle_publisher(self._visualization_img_pub)
                self._visualization_img_pub = None

            # 清理其他资源
            self._cv_bridge = None
            self._camera_matrix = None
            self._dist_coeffs = None
            self._manual_transform_matrix = None

            self.get_logger().info('节点清理完成')
            return TransitionCallbackReturn.SUCCESS

        except Exception as e:
            self.get_logger().error(f'清理节点失败: {str(e)}')
            return TransitionCallbackReturn.FAILURE

    def on_shutdown(self, state: State) -> TransitionCallbackReturn:
        """关闭回调函数：最终清理"""
        self.get_logger().info('关闭节点...')
        self.get_logger().info('节点已关闭')
        return TransitionCallbackReturn.SUCCESS

    def project_3d_bbox_to_image(self, center_point, width, height, depth, camera_matrix):
        """
        将3D包围盒投影到图像平面
        
        参数:
            center_point: 3D包围盒中心点 [x, y, z]
            width: 包围盒宽度 (x轴方向)
            height: 包围盒高度 (y轴方向)
            depth: 包围盒深度 (z轴方向)
            camera_matrix: 相机内参矩阵
            
        返回:
            image_points: 包围盒8个顶点在图像上的投影坐标 (u, v)
        """
        # 计算3D包围盒的8个顶点坐标
        x, y, z = center_point
        half_width = width / 2
        half_height = height / 2
        half_depth = depth / 2
        
        # 相机坐标系中的3D包围盒顶点坐标:
        # x轴：相机水平向右
        # y轴：相机垂直向下
        # z轴：相机光轴方向（向前）
        bbox_3d = np.array([
            [x - half_width, y + half_height, z - half_depth],  # 左下前
            [x - half_width, y - half_height, z - half_depth],  # 左上前
            [x + half_width, y - half_height, z - half_depth],  # 右上前
            [x + half_width, y + half_height, z - half_depth],  # 右下前
            [x - half_width, y + half_height, z + half_depth],  # 左下后
            [x - half_width, y - half_height, z + half_depth],  # 左上后
            [x + half_width, y - half_height, z + half_depth],  # 右上后
            [x + half_width, y + half_height, z + half_depth]   # 右下后
        ])
        
        # 投影到图像平面
        image_points = []
        for point in bbox_3d:
            # 只考虑相机前方的点 (z > 0)
            if point[2] <= 0:
                return None  # 如果有任何顶点在相机后方，返回None
                
            # 投影到图像平面
            uv = np.dot(camera_matrix, point)
            u = int(uv[0] / point[2])
            v = int(uv[1] / point[2])
            image_points.append((u, v))
            
        return image_points

    def draw_3d_bbox(self, image, image_points, color=(0, 255, 0), thickness=2):
        """
        在图像上绘制3D包围盒
        
        参数:
            image: 要绘制的图像
            image_points: 包围盒8个顶点在图像上的投影坐标 [(u1,v1), (u2,v2), ...]
            color: 线条颜色 (B,G,R)
            thickness: 线条粗细
        """
        if image_points is None:
            return
            
        # 前面的4个点 (0,1,2,3)
        cv2.line(image, image_points[0], image_points[1], color, thickness)  # 左下前 -> 左上前
        cv2.line(image, image_points[1], image_points[2], color, thickness)  # 左上前 -> 右上前
        cv2.line(image, image_points[2], image_points[3], color, thickness)  # 右上前 -> 右下前
        cv2.line(image, image_points[3], image_points[0], color, thickness)  # 右下前 -> 左下前
        
        # 后面的4个点 (4,5,6,7)
        cv2.line(image, image_points[4], image_points[5], color, thickness)  # 左下后 -> 左上后
        cv2.line(image, image_points[5], image_points[6], color, thickness)  # 左上后 -> 右上后
        cv2.line(image, image_points[6], image_points[7], color, thickness)  # 右上后 -> 右下后
        cv2.line(image, image_points[7], image_points[4], color, thickness)  # 右下后 -> 左下后
        
        # 连接前后的对应点
        cv2.line(image, image_points[0], image_points[4], color, thickness)  # 左下前 -> 左下后
        cv2.line(image, image_points[1], image_points[5], color, thickness)  # 左上前 -> 左上后
        cv2.line(image, image_points[2], image_points[6], color, thickness)  # 右上前 -> 右上后
        cv2.line(image, image_points[3], image_points[7], color, thickness)  # 右下前 -> 右下后
        
        return image

    def apply_transform_manually(self, point):
        """
        使用手动变换矩阵将点从雷达坐标系转换到相机坐标系
        """
        # 创建齐次坐标
        p_homogeneous = np.array([point[0], point[1], point[2], 1.0])

        # 应用变换
        p_transformed = np.dot(self._manual_transform_matrix, p_homogeneous)

        # 返回3D点
        return [p_transformed[0], p_transformed[1], p_transformed[2]]

    def camera_info_callback(self, camera_info_msg):
        """
        处理相机信息的回调函数，只需接收一次
        """
        if not self._camera_info_received:
            self.get_logger().info('接收到相机信息')
            self._camera_matrix = np.array(camera_info_msg.k).reshape(3, 3)
            self._dist_coeffs = np.array(camera_info_msg.d)
            self._camera_info_received = True
            
            if self._debug:
                self.get_logger().debug(f'相机矩阵:\n{self._camera_matrix}')
                self.get_logger().debug(f'畸变系数: {self._dist_coeffs}')
            
            # 接收到信息后销毁订阅者
            self.destroy_subscription(self._camera_info_sub)
            self._camera_info_sub = None
            self.get_logger().info('相机信息已获取，已取消订阅')

    def sync_callback(self, image_msg, yolo_msg, pointcloud_msg):
        """
        同步回调函数，处理相机图像、YOLO检测和点云数据
        """
        # 检查是否已接收相机信息
        if not self._camera_info_received:
            self.get_logger().warn('尚未接收到相机信息，跳过处理')
            return
            
        # 无论是否处于调试模式，都打印这条消息以确认回调被触发
        self.get_logger().info('同步回调函数被触发')

        callback_start_time = time.time()
        self._frame_count += 1

        # 无论是否处于调试模式，都打印这些基本信息
        self.get_logger().info(f'收到同步数据 (第{self._frame_count}帧)')
        self.get_logger().info(f'图像时间戳: {image_msg.header.stamp.sec}.{image_msg.header.stamp.nanosec}')
        self.get_logger().info(f'点云时间戳: {pointcloud_msg.header.stamp.sec}.{pointcloud_msg.header.stamp.nanosec}')
        self.get_logger().info(f'YOLO时间戳: {yolo_msg.header.stamp.sec}.{yolo_msg.header.stamp.nanosec}')

        # 计算帧率
        current_time = time.time()
        if current_time - self._last_log_time >= 5.0:  # 每5秒记录一次
            fps = self._frame_count / (current_time - self._last_log_time)
            self.get_logger().info(f'处理帧率: {fps:.2f} fps')
            self._frame_count = 0
            self._last_log_time = current_time

        # 将图像转换为OpenCV格式
        start_time = time.time()
        try:
            cv_image = self._cv_bridge.imgmsg_to_cv2(image_msg, "bgr8")
            if self._debug:
                self.get_logger().debug(f'图像转换完成，尺寸: {cv_image.shape}')
        except Exception as e:
            self.get_logger().error(f'图像转换失败: {str(e)}')
            return
        if self._debug:
            self.get_logger().debug(f'图像转换耗时: {time.time() - start_time:.4f}秒')

        # 转换点云数据
        try:
            # 从PointCloud2消息中提取点云
            start_time = time.time()
            pc_points = []
            for p in pc2.read_points(pointcloud_msg, field_names=("x", "y", "z"), skip_nans=True):
                pc_points.append(p)

            pc_extraction_time = time.time() - start_time
            if self._debug:
                self.get_logger().debug(f'点云数据提取完成，点数: {len(pc_points)}，耗时: {pc_extraction_time:.4f}秒')

            if not pc_points:
                self.get_logger().warn('点云为空')
                return

            # 将点云从雷达坐标系转换到相机坐标系
            start_time = time.time()
            transformed_points = []

            if self._use_manual_transform:
                # 使用手动变换矩阵
                for p in pc_points:
                    transformed_point = self.apply_transform_manually(p)
                    transformed_points.append(transformed_point)

                if self._debug:
                    self.get_logger().debug('使用手动变换矩阵进行坐标转换')
            else:
                # 使用TF树查询变换
                # TODO 修改以提高代码运行效率
                try:
                    trans_lidar_to_camera = self._tf_buffer.lookup_transform(
                        self._camera_frame,
                        self._lidar_frame,
                        rclpy.time.Time(),
                        rclpy.duration.Duration(seconds=1.0)
                    )

                    for p in pc_points:
                        # 创建一个点消息
                        point = Point()
                        point.x = p[0]
                        point.y = p[1]
                        point.z = p[2]

                        # 应用变换
                        transformed_point = do_transform_point(point, trans_lidar_to_camera)
                        transformed_points.append([
                            transformed_point.x,
                            transformed_point.y,
                            transformed_point.z
                        ])

                    if self._debug:
                        self.get_logger().debug(f'获取{self._lidar_frame}到{self._camera_frame}的变换成功')
                except (tf2_ros.LookupException, tf2_ros.ConnectivityException, tf2_ros.ExtrapolationException) as e:
                    self.get_logger().error(f'获取变换失败: {str(e)}')
                    return

            transform_time = time.time() - start_time
            if self._debug:
                self.get_logger().debug(f'点云坐标转换完成，耗时: {transform_time:.4f}秒')

            # 将点投影到图像平面
            start_time = time.time()
            projected_points = []
            valid_indices = []

            for i, p in enumerate(transformed_points):
                # 只考虑相机前方的点 (z > 0)
                if p[2] <= 0:
                    continue

                # 投影到图像平面
                uv = np.dot(self._camera_matrix, np.array([p[0], p[1], p[2]]))
                u = int(uv[0] / p[2])
                v = int(uv[1] / p[2])

                # 检查点是否在图像内
                if 0 <= u < cv_image.shape[1] and 0 <= v < cv_image.shape[0]:
                    projected_points.append((u, v, p[0], p[1], p[2]))
                    valid_indices.append(i)

            projection_time = time.time() - start_time
            if self._debug:
                self.get_logger().debug(f'点云投影完成，有效投影点数: {len(projected_points)}，耗时: {projection_time:.4f}秒')

            # 处理YOLO检测结果
            start_time = time.time()
            pose_array = PoseArray()
            pose_array.header = Header()
            pose_array.header.stamp = self.get_clock().now().to_msg()
            pose_array.header.frame_id = self._target_frame

            marker_array = MarkerArray()

            marker_id = 0
            person_count = 0

            # 无论是否处于调试模式，都打印YOLO检测结果数量
            self.get_logger().info(f'YOLO检测结果数量: {len(yolo_msg.detections)}')

            for detection in yolo_msg.detections:
                # 仅处理人类检测 (class_id=0 对应人类)
                self.get_logger().info(f'检测类别: {detection.class_name}, 置信度: {detection.score}')
                if detection.class_name != "person":
                    self.get_logger().info(f'跳过非人类检测: {detection.class_name}')
                    continue

                person_count += 1
                self.get_logger().info(f'处理第 {person_count} 个人体检测')
                detection_start_time = time.time()

                # 获取检测框
                bbox = detection.bbox
                bbox_center_x = bbox.center.position.x
                bbox_center_y = bbox.center.position.y
                bbox_width = bbox.size.x
                bbox_height = bbox.size.y

                # 计算检测框边界
                x_min = int(bbox_center_x - bbox_width / 2)
                y_min = int(bbox_center_y - bbox_height / 2)
                x_max = int(bbox_center_x + bbox_width / 2)
                y_max = int(bbox_center_y + bbox_height / 2)

                if self._debug:
                    self.get_logger().debug(f'检测到人类: 置信度={detection.score:.4f}, 边界框=[{x_min},{y_min},{x_max},{y_max}]')

                # 提取检测框内的点云
                box_points = []
                for p in projected_points:
                    u, v, x, y, z = p
                    if x_min <= u <= x_max and y_min <= v <= y_max:
                        box_points.append((x, y, z))

                if len(box_points) < self._min_points_in_box:
                    self.get_logger().info(f'检测框内点云数量不足: {len(box_points)} < {self._min_points_in_box}')
                    continue

                self.get_logger().info(f'检测框内有效点云数量: {len(box_points)}')

                # 对检测框内的点云进行聚类
                points_np = np.array(box_points, dtype=np.float32)

                # 计算点云中心点（简单方法）
                center_point = np.mean(points_np, axis=0)

                # 估计3D边界框尺寸（简化为固定尺寸的人体模型）
                # 标准人体模型：宽度0.5m，深度0.5m，高度1.8m
                human_width = 0.5
                human_depth = 0.5
                human_height = 1.8

                # 创建人体姿态
                human_pose = Pose()
                human_pose.position.x = float(center_point[0])
                human_pose.position.y = float(center_point[1])
                human_pose.position.z = float(center_point[2])

                # 简化：假设人体朝向与相机方向一致
                human_pose.orientation.w = 1.0

                # 添加到姿态数组
                pose_array.poses.append(human_pose)

                self.get_logger().info(f'人体位置: x={center_point[0]:.2f}, y={center_point[1]:.2f}, z={center_point[2]:.2f}')
                self.get_logger().info(f'处理单个人体检测耗时: {time.time() - detection_start_time:.4f}秒')

                # 创建可视化标记
                if self._visualize:
                    # 人体立方体标记
                    cube_marker = Marker()
                    cube_marker.header = Header()
                    cube_marker.header.frame_id = self._camera_frame
                    cube_marker.header.stamp = self.get_clock().now().to_msg()
                    cube_marker.id = marker_id
                    marker_id += 1
                    cube_marker.type = Marker.CUBE
                    cube_marker.action = Marker.ADD

                    # 设置位置和方向
                    cube_marker.pose = human_pose

                    # 设置尺寸
                    cube_marker.scale = Vector3(x=human_depth, y=human_width, z=human_height)

                    # 设置颜色（半透明绿色）
                    cube_marker.color = ColorRGBA(r=0.0, g=1.0, b=0.0, a=0.3)

                    # 设置持续时间（2秒）
                    cube_marker.lifetime = rclpy.duration.Duration(seconds=2.0).to_msg()

                    marker_array.markers.append(cube_marker)

                    # 人体ID文本标记
                    text_marker = Marker()
                    text_marker.header = Header()
                    text_marker.header.frame_id = self._camera_frame
                    text_marker.header.stamp = self.get_clock().now().to_msg()
                    text_marker.id = marker_id
                    marker_id += 1
                    text_marker.type = Marker.TEXT_VIEW_FACING
                    text_marker.action = Marker.ADD

                    # 文本位置（稍微向上偏移）
                    text_marker.pose = human_pose
                    text_marker.pose.position.z += human_height / 2 + 0.2

                    # 文本内容和大小
                    text_marker.text = f"Person {detection.score:.2f}"
                    text_marker.scale.z = 0.5  # 文本大小

                    # 白色文本
                    text_marker.color = ColorRGBA(r=1.0, g=1.0, b=1.0, a=0.8)

                    # 设置持续时间（2秒）
                    text_marker.lifetime = rclpy.duration.Duration(seconds=2.0).to_msg()

                    marker_array.markers.append(text_marker)

            yolo_process_time = time.time() - start_time
            self.get_logger().info(f'YOLO检测处理完成，检测到{person_count}人，耗时: {yolo_process_time:.4f}秒')

            # 创建图像可视化（在发布之前）
            if self._visualize and person_count > 0:
                visualization_img = cv_image.copy()
                vis_marker_count = 0
                
                # 为每个检测到的人绘制3D包围盒
                bbox_index = 0
                for detection in yolo_msg.detections:
                    if detection.class_name != "person":
                        continue
                        
                    # 获取检测框信息用于估计人体尺寸
                    bbox = detection.bbox
                    bbox_width = bbox.size.x
                    bbox_height = bbox.size.y
                    
                    # 根据2D检测框比例估计人体尺寸
                    # 宽高比 = 检测框宽度/高度
                    aspect_ratio = bbox_width / bbox_height if bbox_height > 0 else 0.5
                    
                    # 使用对应的3D位置信息
                    if bbox_index < len(pose_array.poses):
                        human_pose = pose_array.poses[bbox_index]
                        center_point = [human_pose.position.x, human_pose.position.y, human_pose.position.z]
                        
                        # 根据实际检测结果动态调整人体尺寸
                        # 基础尺寸
                        human_height = 1.7  # 标准人体高度(米)
                        human_width = human_height * aspect_ratio * 0.4  # 宽度与高度成比例
                        human_depth = human_width * 0.8  # 人体厚度通常小于宽度
                        
                        # 基于距离进行尺寸校正 (越远的人体看起来应该越小)
                        distance = np.sqrt(center_point[0]**2 + center_point[1]**2 + center_point[2]**2)
                        distance_factor = max(0.9, min(1.1, 1.0 + (distance - 3.0) * 0.1))
                        
                        human_height *= distance_factor
                        human_width *= distance_factor
                        human_depth *= distance_factor
                        
                        self.get_logger().info(f'估计人体尺寸: 高={human_height:.2f}m, 宽={human_width:.2f}m, 深={human_depth:.2f}m, 距离={distance:.2f}m')
                        
                        # 投影3D包围盒到图像
                        image_points = self.project_3d_bbox_to_image(
                            center_point, human_width, human_height, human_depth, self._camera_matrix
                        )
                        
                        # 绘制3D包围盒
                        if image_points is not None:
                            bbox_color = (self._bbox_color_b, self._bbox_color_g, self._bbox_color_r)  # OpenCV使用BGR顺序
                            self.draw_3d_bbox(visualization_img, image_points, color=bbox_color, thickness=self._bbox_line_thickness)
                            vis_marker_count += 1
                            
                    bbox_index += 1
                    
                self.get_logger().info(f'在图像上绘制了 {vis_marker_count} 个3D包围盒')
                
                # 发布可视化图像
                if self._visualization_img_pub.is_activated:
                    try:
                        img_msg = self._cv_bridge.cv2_to_imgmsg(visualization_img, "bgr8")
                        img_msg.header.stamp = self.get_clock().now().to_msg()
                        img_msg.header.frame_id = image_msg.header.frame_id
                        self._visualization_img_pub.publish(img_msg)
                        self.get_logger().info('发布了可视化图像')
                    except Exception as e:
                        self.get_logger().error(f'发布可视化图像失败: {str(e)}')
                else:
                    self.get_logger().warn('可视化图像发布者未激活，无法发布图像')

            # 发布结果，确保发布者处于激活状态
            publish_start_time = time.time()
            if self._detection_pub.is_activated:
                self.get_logger().info(f'发布人体检测结果，数量: {len(pose_array.poses)}')
                self._detection_pub.publish(pose_array)
            else:
                self.get_logger().warn('检测发布者未激活，无法发布结果')

            if self._visualize and marker_array.markers and self._marker_pub.is_activated:
                self.get_logger().info(f'发布可视化标记，数量: {len(marker_array.markers)}')
                self._marker_pub.publish(marker_array)
            elif self._visualize and marker_array.markers:
                self.get_logger().warn('标记发布者未激活，无法发布可视化标记')

            publish_time = time.time() - publish_start_time
            self.get_logger().info(f'发布结果耗时: {publish_time:.4f}秒')

            callback_end_time = time.time()
            total_time = callback_end_time - callback_start_time
            if self._debug:
                self.get_logger().debug(f'回调总耗时: {total_time:.4f}秒')

        except Exception as e:
            import traceback
            self.get_logger().error(f'处理失败: {str(e)}')
            self.get_logger().error(f'异常详情: {traceback.format_exc()}')


def main(args=None):
    rclpy.init(args=args)

    node = HumanFusion3DNode()

    # 手动触发配置
    config_result = node.trigger_configure()
    if config_result == TransitionCallbackReturn.FAILURE:
        node.get_logger().error('节点配置失败')
        node.destroy_node()
        rclpy.shutdown()
        return

    # 手动触发激活
    activate_result = node.trigger_activate()
    if activate_result == TransitionCallbackReturn.FAILURE:
        node.get_logger().error('节点激活失败')
        node.destroy_node()
        rclpy.shutdown()
        return

    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    except Exception as e:
        node.get_logger().error(f'运行节点时发生错误: {str(e)}')
    finally:
        # 更优雅的关闭流程
        node.trigger_deactivate()
        node.trigger_cleanup()
        node.trigger_shutdown()
        node.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main()