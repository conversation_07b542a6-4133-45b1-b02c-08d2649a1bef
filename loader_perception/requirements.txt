numpy<2
opencv-python>=4.8.1.78
typing-extensions>=4.4.0
ultralytics==8.3.91
lap>=0.5.12

# certifi - 2025.1.31
# charset-normalizer - 3.4.1
# filelock - 3.18.0
# fsspec - 2025.3.2
# idna - 3.10
# lap - 0.5.12
# mpmath - 1.3.0
# networkx - 3.4.2
# numpy - 1.26.4
# nvidia-cublas-cu12 - 12.4.5.8
# nvidia-cuda-cupti-cu12 - 12.4.127
# nvidia-cuda-nvrtc-cu12 - 12.4.127
# nvidia-cuda-runtime-cu12 - 12.4.127
# nvidia-cudnn-cu12 - 9.1.0.70
# nvidia-cufft-cu12 - 11.2.1.3
# nvidia-curand-cu12 - 10.3.5.147
# nvidia-cusolver-cu12 - 11.6.1.9
# nvidia-cusparse-cu12 - 12.3.1.170
# nvidia-cusparselt-cu12 - 0.6.2
# nvidia-nccl-cu12 - 2.21.5
# nvidia-nvjitlink-cu12 - 12.4.127
# nvidia-nvtx-cu12 - 12.4.127
# opencv-python - 4.11.0.86
# pandas - 2.2.3
# py-cpuinfo - 9.0.0
# python-dateutil - 2.9.0.post0
# requests - 2.32.3
# seaborn - 0.13.2
# sympy - 1.13.1
# torch - 2.6.0
# torchvision - 0.21.0
# tqdm - 4.67.1
# triton - 3.2.0
# typing-extensions - 4.13.2
# tzdata - 2025.2
# ultralytics - 8.3.91
# ultralytics-thop - 2.0.14
# urllib3 - 2.4.0