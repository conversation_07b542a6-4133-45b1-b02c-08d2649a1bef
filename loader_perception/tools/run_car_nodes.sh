#!/bin/bash

# 设置ROS2环境
source /opt/ros/humble/setup.bash
# 工作空间环境设置（确保路径正确）
source /root/loader_ws/install/setup.bash

# 启动静态TF发布节点
echo "启动静态TF发布节点..."
python3 /root/loader_ws/src/loader_perception/tools/car_static_tf_pb.py &
TF_PID=$!

# 启动相机信息发布节点
echo "启动相机信息发布节点..."
python3 /root/loader_ws/src/loader_perception/tools/car_camera_info_pb.py &
CAM_PID=$!

echo "所有节点已启动！按 Ctrl+C 终止..."

# 捕获SIGINT信号并优雅地关闭所有进程
trap 'echo "正在关闭节点..."; kill $TF_PID $CAM_PID; wait $TF_PID $CAM_PID 2>/dev/null; echo "已关闭所有节点"; exit' SIGINT

# 等待所有后台进程
wait 