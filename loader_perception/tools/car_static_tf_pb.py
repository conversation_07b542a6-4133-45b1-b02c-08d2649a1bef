#!/usr/bin/env python3
import rclpy
from rclpy.node import Node
from tf2_ros import StaticTransformBroadcaster
from geometry_msgs.msg import TransformStamped
import numpy as np
import math

class StaticTfPublisher(Node):
    def __init__(self):
        super().__init__('static_tf_publisher')
        self.tf_broadcaster = StaticTransformBroadcaster(self)

        # First transform: base_link -> rslidar
        transform1 = TransformStamped()
        transform1.header.stamp = self.get_clock().now().to_msg()
        transform1.header.frame_id = 'base_link'       # 父坐标系
        transform1.child_frame_id = 'rslidar'          # 子坐标系

        # 设置平移
        transform1.transform.translation.x = 0.0
        transform1.transform.translation.y = 0.0
        transform1.transform.translation.z = 0.43

        # 设置旋转（四元数）
        transform1.transform.rotation.x = 0.0
        transform1.transform.rotation.y = 0.0
        transform1.transform.rotation.z = 0.0
        transform1.transform.rotation.w = 1.0

        # Second transform: rslidar -> camera (更新后的变换矩阵)
        transform2 = TransformStamped()
        transform2.header.stamp = self.get_clock().now().to_msg()
        transform2.header.frame_id = 'rslidar'       # 父坐标系 (radar frame)
        transform2.child_frame_id = 'camera'         # 子坐标系 (camera frame)

        # 设置平移（从变换矩阵第4列）
        transform2.transform.translation.x = 0.0375
        transform2.transform.translation.y = -0.1166
        transform2.transform.translation.z = -0.1435

        # 从3x3旋转矩阵部分提取四元数
        rotation_matrix = np.array([
            [0.0065, -0.9999, 0.0139],
            [-0.0045, -0.0139, -0.9999],
            [1.0000, 0.0065, -0.0046]
        ])
        
        # 将旋转矩阵转换为四元数
        quaternion = self.rotation_matrix_to_quaternion(rotation_matrix)
        
        # 设置旋转（四元数）
        transform2.transform.rotation.w = 1.
        transform2.transform.rotation.x = 0.
        transform2.transform.rotation.y = 0.
        transform2.transform.rotation.z = 0.

        # 一次性发布两个变换
        self.tf_broadcaster.sendTransform([transform1, transform2])
        self.get_logger().info("Static TFs published: base_link -> rslidar and rslidar -> camera")

    def rotation_matrix_to_quaternion(self, matrix):
        """将旋转矩阵转换为四元数"""
        q = np.zeros(4)
        trace = matrix[0, 0] + matrix[1, 1] + matrix[2, 2]
        
        if trace > 0:
            s = 0.5 / math.sqrt(trace + 1.0)
            q[0] = 0.25 / s  # w
            q[1] = (matrix[2, 1] - matrix[1, 2]) * s  # x
            q[2] = (matrix[0, 2] - matrix[2, 0]) * s  # y
            q[3] = (matrix[1, 0] - matrix[0, 1]) * s  # z
        else:
            if matrix[0, 0] > matrix[1, 1] and matrix[0, 0] > matrix[2, 2]:
                s = 2.0 * math.sqrt(1.0 + matrix[0, 0] - matrix[1, 1] - matrix[2, 2])
                q[0] = (matrix[2, 1] - matrix[1, 2]) / s  # w
                q[1] = 0.25 * s  # x
                q[2] = (matrix[0, 1] + matrix[1, 0]) / s  # y
                q[3] = (matrix[0, 2] + matrix[2, 0]) / s  # z
            elif matrix[1, 1] > matrix[2, 2]:
                s = 2.0 * math.sqrt(1.0 + matrix[1, 1] - matrix[0, 0] - matrix[2, 2])
                q[0] = (matrix[0, 2] - matrix[2, 0]) / s  # w
                q[1] = (matrix[0, 1] + matrix[1, 0]) / s  # x
                q[2] = 0.25 * s  # y
                q[3] = (matrix[1, 2] + matrix[2, 1]) / s  # z
            else:
                s = 2.0 * math.sqrt(1.0 + matrix[2, 2] - matrix[0, 0] - matrix[1, 1])
                q[0] = (matrix[1, 0] - matrix[0, 1]) / s  # w
                q[1] = (matrix[0, 2] + matrix[2, 0]) / s  # x
                q[2] = (matrix[1, 2] + matrix[2, 1]) / s  # y
                q[3] = 0.25 * s  # z
        
        return q

def main(args=None):
    rclpy.init(args=args)
    node = StaticTfPublisher()
    rclpy.spin(node)
    rclpy.shutdown()

if __name__ == '__main__':
    main()