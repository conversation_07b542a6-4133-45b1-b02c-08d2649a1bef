#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from sensor_msgs.msg import CameraInfo
from std_msgs.msg import Header
from builtin_interfaces.msg import Time
from rclpy.qos import QoSProfile, QoSReliabilityPolicy, QoSHistoryPolicy

class CameraInfoPublisher(Node):
    def __init__(self):
        super().__init__('camera_info_publisher')
        
        # Create QoS profile
        qos_profile = QoSProfile(
            reliability=QoSReliabilityPolicy.RELIABLE,
            history=QoSHistoryPolicy.KEEP_LAST,
            depth=10
        )
        
        # Create publisher
        self.publisher = self.create_publisher(
            CameraInfo,
            '/camera/color/camera_info',
            qos_profile
        )
        
        # Set timer for periodic publishing (10Hz)
        self.timer = self.create_timer(0.1, self.publish_camera_info)
        
        self.get_logger().info('Camera Info Publisher Node has been started')
        
    def publish_camera_info(self):
        # Create camera info message
        msg = CameraInfo()
        
        # Set header
        msg.header = Header()
        msg.header.stamp = self.get_clock().now().to_msg()
        msg.header.frame_id = 'camera'
        
        # Set image dimensions
        msg.height = 480
        msg.width = 640
        
        # Set distortion model and coefficients
        msg.distortion_model = 'plumb_bob'
        msg.d = [0.0, 0.0, 0.0, 0.0, 0.0]
        
        # Set intrinsic camera matrix (K)
        msg.k = [
            607.159912109375, 0.0, 318.06793212890625,
            0.0, 607.1823120117188, 253.84774780273438,
            0.0, 0.0, 1.0
        ]
        
        # Set rotation matrix (R)
        msg.r = [
            1.0, 0.0, 0.0,
            0.0, 1.0, 0.0,
            0.0, 0.0, 1.0
        ]
        
        # Set projection matrix (P)
        msg.p = [
            607.159912109375, 0.0, 318.06793212890625, 0.0,
            0.0, 607.1823120117188, 253.84774780273438, 0.0,
            0.0, 0.0, 1.0, 0.0
        ]
        
        # Set binning
        msg.binning_x = 0
        msg.binning_y = 0
        
        # Set ROI
        msg.roi.x_offset = 0
        msg.roi.y_offset = 0
        msg.roi.height = 0
        msg.roi.width = 0
        msg.roi.do_rectify = False
        
        # Publish the message
        self.publisher.publish(msg)

def main(args=None):
    rclpy.init(args=args)
    
    node = CameraInfoPublisher()
    
    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        node.get_logger().info('Keyboard interrupt, shutting down')
    finally:
        node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()