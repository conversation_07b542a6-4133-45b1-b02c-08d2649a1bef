import numpy as np
from scipy.spatial.transform import Rotation

def matrix_to_quaternion_and_axis_angle(rotation_matrix, force_orthogonal=False):
    """
    输入：3x3旋转矩阵（numpy数组）
    输出：四元数（[x, y, z, w]格式）和轴-角表示（旋转轴单位向量，角度弧度值）
    
    参数：
    rotation_matrix: 3x3旋转矩阵
    force_orthogonal: 如果为True，将强制使矩阵正交化
    """
    # 如果需要，进行正交化处理
    if force_orthogonal:
        u, s, vh = np.linalg.svd(rotation_matrix)
        rotation_matrix = u @ vh
    
    # 验证是否为有效旋转矩阵
    det = np.linalg.det(rotation_matrix)
    if not np.isclose(det, 1.0, atol=1e-6):
        print(f"警告：输入矩阵行列式为 {det}，不是精确的1")
        if not force_orthogonal:
            raise ValueError("输入的矩阵不是有效的旋转矩阵（行列式不为1）。尝试设置force_orthogonal=True")
    
    # 使用SciPy库转换
    rotation = Rotation.from_matrix(rotation_matrix)
    quaternion = rotation.as_quat()  # 格式为[x, y, z, w]
    axis_angle = rotation.as_rotvec()  # 旋转向量（轴 * 角度）
    
    # 计算角度和单位轴
    angle = np.linalg.norm(axis_angle)
    axis = axis_angle / angle if angle != 0 else np.array([0, 0, 0])
    
    return quaternion, (axis, angle)

# 示例输入
if __name__ == "__main__":
    try:
        # 替换为有效的旋转矩阵（绕Z轴旋转90度）
        R_valid = np.array([
            [1, 0, 0],
            [0, 1, 0],
            [0, 0, 1]
        ])
        
        # 原始可能无效的矩阵
        R_original = np.array([
            [0.0065, -0.9999, 0.0139],
            [-0.0045, -0.0139, -0.9999],
            [1.0000, 0.0065, -0.0046]
        ])
        
        print("有效旋转矩阵示例结果：")
        quat, (axis, angle) = matrix_to_quaternion_and_axis_angle(R_valid)
        print("四元数 (x, y, z, w):", quat)
        print("旋转轴:", axis)
        print("旋转角度 (弧度):", angle)
        print("旋转角度 (度数):", np.degrees(angle))
        
        print("\n原始矩阵结果（使用强制正交化）：")
        quat2, (axis2, angle2) = matrix_to_quaternion_and_axis_angle(R_original, force_orthogonal=True)
        print("四元数 (x, y, z, w):", quat2)
        print("旋转轴:", axis2)
        print("旋转角度 (弧度):", angle2)
        print("旋转角度 (度数):", np.degrees(angle2))
        
        # 输出原始矩阵行列式
        print("\n原始矩阵行列式:", np.linalg.det(R_original))
        
    except Exception as e:
        print(f"错误：{e}")
        print("请尝试降级NumPy版本，命令：pip install numpy==1.24.3")