# loader_perception 项目安装与使用指南

本文档提供了 loader_perception 项目的详细安装步骤和使用方法。loader_perception 是一个基于 ROS 2 的大型船舶装载机船舱清仓作业感知系统，包含多个功能模块，如人员检测、相机-激光雷达标定验证等。

团队协作文档地址：https://www.yuque.com/yinmaoliang/fqrnn7

数据存储地址：http://10.20.25.159:8050/login?redirect=/files/
 - 账号：loader-data
 - 密码：loader

> **注意**：指南可能存在问题，需要大家反馈更正。

## 目录

- [系统要求](#系统要求)
- [安装步骤](#安装步骤)
  - [1. 安装 ROS 2](#1-安装-ros-2)
  - [2. 创建工作空间](#2-创建工作空间)
  - [3. 克隆项目代码](#3-克隆项目代码)
  - [4. 安装依赖项](#4-安装依赖项)
  - [5. 编译项目](#5-编译项目)
- [功能模块](#功能模块)
  - [人员检测模块](#人员检测模块)
  - [相机-激光雷达标定验证工具](#相机-激光雷达标定验证工具)
  - [人体3D融合检测](#人体3d融合检测)
- [使用方法](#使用方法)
  - [启动基础节点](#启动基础节点)
  - [运行人员检测](#运行人员检测)
  - [运行标定验证](#运行标定验证)
  - [运行人体3D融合检测](#运行人体3d融合检测)
- [常见问题](#常见问题)
- [开发者指南](#开发者指南)

## 系统要求

- 操作系统：Ubuntu 20.04/22.04 (推荐)
- ROS 2 版本：Foxy/Humble
- Python 版本：3.10+
- GPU：NVIDIA GPU (推荐，用于运行深度学习模型，例如： YOLO 模型、场景的特征提取等)
- 硬件：
  - 相机 (RGB 或 RGB-D)
  - 激光雷达 (可选，用于 3D 检测)

## 安装步骤

### 1. 安装 ROS 2

如果您尚未安装 ROS 2 Humble，请按照 [ROS 2 官方安装指南](https://docs.ros.org/en/humble/Installation.html) 进行安装。

简要步骤包括(如果没有代理建议使用鱼香ROS一键安装)：
```bash
# 设置软件源
sudo apt update && sudo apt install -y curl gnupg lsb-release
sudo curl -sSL https://raw.githubusercontent.com/ros/rosdistro/master/ros.key -o /usr/share/keyrings/ros-archive-keyring.gpg
echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/ros-archive-keyring.gpg] http://packages.ros.org/ros2/ubuntu $(source /etc/os-release && echo $UBUNTU_CODENAME) main" | sudo tee /etc/apt/sources.list.d/ros2.list > /dev/null

# 安装 ROS 2 Humble
sudo apt update
sudo apt install -y ros-humble-desktop

# 安装依赖工具
sudo apt install -y python3-colcon-common-extensions python3-rosdep python3-vcstool
sudo rosdep init
rosdep update
```
鱼香ROS一键安装参考：
 - https://fishros.org.cn/forum/topic/3923/ros%E4%B8%80%E9%94%AE%E5%AE%89%E8%A3%85
 - https://blog.csdn.net/m0_73745340/article/details/135281023

### 2. 创建工作空间

```bash
mkdir -p ~/loader_ws/src
cd ~/loader_ws
```

### 3. 克隆项目代码

```bash
cd ~/loader_ws/src
git clone https://github.com/Yinmlmaoliang/loader_perception.git
```
注：如果校园网git不下来，多试几次，最好是使用代理

### 4. 安装依赖项

#### 安装 ROS 2 依赖

```bash
cd ~/loader_ws
rosdep install --from-paths src --ignore-src -r -y
```

#### 安装 Python 依赖

```bash
cd ~/loader_ws/src/loader_perception
pip install -r requirements.txt
```

对于特定模块的依赖：

```bash
# 标定验证工具依赖
pip install -r calibration_validator/requirements.txt
```

> **注意**：对于 NVIDIA Jetson 设备（如 Jetson NX），torch 和 torchvision 不能直接通过 pip 安装，需要按照 NVIDIA 官方指南安装适配版本。参考项目协作文档：https://www.yuque.com/yinmaoliang/fqrnn7/yloz7w3bygxd9usx?singleDoc# 《Nvidia Jeston Oiri NX 安装一些特殊的python依赖》

### 5. 编译项目

```bash
cd ~/loader_ws
colcon build --symlink-install
source install/setup.bash
```

如果只需编译特定包，可以使用：

```bash
colcon build --symlink-install --packages-select <包名>
```

例如：
```bash
colcon build --symlink-install --packages-select yolo_msgs yolo_ros yolo_bringup
```

## 功能模块

### 人员检测模块

人员检测模块基于 YOLO 系列目标检测模型，支持 2D 和 3D 检测，并集成了追踪功能。

#### 主要特点

- 支持多种 YOLO 模型（YOLOv5/8/9/10/11/12 和 YOLO-World）
- 支持 2D 检测（边界框、掩码、关键点）
- 支持 3D 检测（通过深度相机）
- 集成目标追踪功能
- 提供可视化调试节点

#### 包结构

```
people_detection/
├── yolo_msgs/           # 自定义消息定义
├── yolo_ros/            # 核心功能实现
└── yolo_bringup/        # 启动文件
```

### 相机-激光雷达标定验证工具

标定验证工具用于验证相机与激光雷达联合标定外参矩阵是否正确，通过将激光雷达点云投影到相机图像上，可以直观地判断标定的准确性。

#### 主要特点

- 订阅相机图像和激光雷达点云数据
- 使用 PyTorch 和 kornia 进行高效的点云变换和投影计算
- 支持 CUDA 加速
- 使用彩色深度图可视化点云投影结果
- 发布可视化结果图像，方便检查标定效果

### 人体3D融合检测

人体3D融合检测节点基于2D检测结果与点云数据进行3D人体检测，通过融合相机图像中的人体检测结果与点云数据，计算出人体的3D位置并发布。

#### 主要特点

- 接收并同步相机图像、相机参数、YOLO检测结果和点云数据
- 将点云从雷达坐标系转换到相机坐标系
- 将点云投影到图像平面
- 提取2D人体检测框内的点云
- 计算人体3D位置
- 发布人体位置和可视化标记

## 使用方法

### 启动基础节点
回放bag数据，发布必要数据话题, 在bag文件夹所在目录下运行：
`ros2 bag play -l rosbag2_2025_04_10-20_12_32/`
> **注意**：bag文件校园网下载地址：http://10.20.25.159:8050/share/dvYJdRWz

在使用系统前，需要先启动基础节点，包括静态 TF 发布和相机信息发布，如果录制的bag中以及包含TF变换以及相机信息话题，不需要手动发布了！这里使用是因为数据包中没有录制：

```bash
cd ~/loader_ws
source install/setup.bash
bash src/loader_perception/tools/run_car_nodes.sh
```

### 运行人员检测
详细说明参考功能包下的[README.md](loader_perception/people_detection/README.md)

#### 基本检测

```bash
# 使用默认参数运行 YOLOv8 检测
ros2 launch yolo_bringup yolov8.launch.py input_image_topic:=/camera/color/image_raw
```

#### 带追踪功能的检测

```bash
ros2 launch yolo_bringup yolov8.launch.py input_image_topic:=/camera/color/image_raw use_tracking:=True
```

#### 带3D检测的 YOLO (未测，录制数据没有深度信息)

```bash
ros2 launch yolo_bringup yolov8.launch.py use_3d:=True
```
> **注意**：这里的2D->3D思路可以借鉴，如果将点云数据转换为深度数据即可直接使用带3D检测的 YOLO实现3D物体检测

#### 使用 YOLO-World 模型

```bash
ros2 launch yolo_bringup yolo-world.launch.py
```

#### 自定义参数示例

```bash
ros2 launch yolo_bringup yolov8.launch.py \
  model:=yolov8m.pt \
  device:=cuda:0 \
  threshold:=0.5 \
  input_image_topic:=/camera/rgb/image_raw
```


### 运行人体3D融合检测
需要运行人员检测节点，发布需要的话题数据
详细说明参考功能包下的[README.md](loader_perception/calibration_validator/README.md)
目前存在的问题整理在团队协作文档：

```bash
# 启动人体3D融合检测节点
ros2 run human_fusion_3d human_fusion_3d_node

# 或使用启动文件
ros2 launch human_fusion_3d human_fusion_3d.launch.py

# debug一些数据使用
ros2 launch human_fusion_3d human_fusion_3d.launch.py --debug 2>&1 | grep -v "\[DEBUG\] \[launch"
```

### 运行标定验证
详细说明参考功能包下的[README.md](loader_perception/calibration_validator/README.md)

```bash
# 启动标定验证节点
ros2 run calibration_validator calibration_validator_node

# 或使用启动文件（包含 RViz 可视化）
ros2 launch calibration_validator calibration_validator.launch.py
```

## 常见问题

### 1. CUDA 相关错误

**问题**：运行时出现 CUDA 相关错误。

**解决方案**：
- 确保已安装正确版本的 CUDA 和 cuDNN
- 检查 GPU 驱动是否兼容

### 2. 模型文件缺失

**问题**：启动 YOLO 节点时提示模型文件缺失。

**解决方案**：
- 一般不会出现问题，运行时自动下载权重，但是由于网络原因下载不成功可尝试代理
- 或者手动下载对应的 YOLO 模型文件（如 yolov8m.pt）到工作空间目录下
- 将模型文件放置在正确的路径下
- 或在启动命令中指定完整的模型文件路径

### 3. 标定验证结果不准确

**问题**：标定验证工具显示的点云投影与图像不匹配。

**解决方案**：
- 检查相机内参是否正确
- 修改 `calibration_validator_node.py` 中的变换矩阵，确保变换矩阵标定正确
- 确保 TF 树中相机和激光雷达的坐标关系正确


---

如有更多问题，请参考项目文档或联系开发团队。
