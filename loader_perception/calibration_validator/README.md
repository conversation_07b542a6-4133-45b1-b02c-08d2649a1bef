# 相机-激光雷达标定验证工具（Calibration Validator）

这个ROS2包提供用于验证相机与激光雷达联合标定外参矩阵是否正确的工具。通过将激光雷达点云投影到相机图像上，可以直观地判断标定的准确性。

## 功能特点

- 订阅相机图像和激光雷达点云数据
- 使用PyTorch和kornia进行高效的点云变换和投影计算
- 支持CUDA加速
- 使用彩色深度图可视化点云投影结果
- 发布可视化结果图像，方便检查标定效果

使用cuda进行投影，但是提取点云还是使用numpy，代码运行效率如下所示：
```bash
[calibration_validator_node-1] [INFO] [1744974741.300887300] [calibration_validator]: 总处理时间: 9.6ms | 图像转换: 0.7ms (7.2%) | 点云提取: 5.5ms (57.9%) | 点云过滤: 0.3ms (2.8%) | 坐标转换: 0.4ms (4.0%) | 点云投影: 0.3ms (3.5%) | 可视化: 2.0ms (20.8%) | 发布结果: 0.3ms (3.6%)
[calibration_validator_node-1] [INFO] [1744974741.361320958] [calibration_validator]: 总处理时间: 16.9ms | 图像转换: 0.4ms (2.6%) | 点云提取: 5.7ms (33.8%) | 点云过滤: 1.3ms (7.6%) | 坐标转换: 1.0ms (6.2%) | 点云投影: 1.3ms (7.7%) | 可视化: 6.5ms (38.4%) | 发布结果: 0.6ms (3.5%)
[calibration_validator_node-1] [INFO] [1744974741.470576648] [calibration_validator]: 总处理时间: 17.4ms | 图像转换: 0.5ms (2.7%) | 点云提取: 5.5ms (31.5%) | 点云过滤: 1.3ms (7.6%) | 坐标转换: 1.1ms (6.2%) | 点云投影: 1.2ms (6.7%) | 可视化: 7.2ms (41.4%) | 发布结果: 0.6ms (3.7%)
```
当代码运行设备改为cpu时，代码运行效率如下：
```bash
[calibration_validator_node-1] [INFO] [1744976604.400484051] [calibration_validator]: 总处理时间: 14.3ms | 图像转换: 0.4ms (2.7%) | 点云提取: 3.4ms (23.8%) | 点云过滤: 0.6ms (4.3%) | 坐标转换: 4.8ms (33.5%) | 点云投影: 1.2ms (8.6%) | 可视化: 3.5ms (24.4%) | 发布结果: 0.3ms (2.4%)
[calibration_validator_node-1] [INFO] [1744976604.598705050] [calibration_validator]: 总处理时间: 22.5ms | 图像转换: 0.8ms (3.4%) | 点云提取: 5.7ms (25.1%) | 点云过滤: 1.3ms (5.9%) | 坐标转换: 11.7ms (51.9%) | 点云投影: 0.4ms (1.6%) | 可视化: 2.2ms (9.6%) | 发布结果: 0.5ms (2.3%)
[calibration_validator_node-1] [INFO] [1744976604.684898979] [calibration_validator]: 总处理时间: 16.1ms | 图像转换: 0.6ms (3.8%) | 点云提取: 5.9ms (36.6%) | 点云过滤: 1.3ms (8.1%) | 坐标转换: 1.2ms (7.4%) | 点云投影: 1.3ms (7.8%) | 可视化: 5.5ms (34.2%) | 发布结果: 0.3ms (2.0%)
```

ROS2节点代码运行效率分析（CPU vs CUDA）

性能对比

CUDA模式
- 总处理时间：约14.6ms
- 点云提取占比高（31-58%）
- 坐标转换速度快（4-6%）
- 可视化部分表现不稳定（20-41%）

CPU模式
- 总处理时间：约17.6ms
- 坐标转换显著变慢（最高达52%）
- 点云提取占比相对较高（24-37%）

主要区别
1. **坐标转换**：CUDA模式下显著优于CPU（0.8ms vs 5.9ms）
2. **总体性能**：CUDA模式整体快约3ms
3. **稳定性**：CPU模式性能波动较大，特别是坐标转换部分

优化建议
1. **点云提取**：将numpy操作迁移到CUDA，可能大幅提升性能
2. **数据传输减少**：避免CPU与GPU之间的数据频繁传输
3. **批处理优化**：对点云数据采用批处理方式
4. **算法优化**：优化可视化算法，减少在CUDA模式下的耗时
5. **内存管理**：改进点云过滤方法，减少内存重分配

**TODO:**
代码进一步提升主要在于将点云提取部分从numpy迁移到CUDA实现，减少CPU-GPU数据传输开销，并优化可视化渲染逻辑。




## 安装依赖

除了基本的ROS2依赖外，本包还需要以下Python库：
注：NX板子需要注意torch torchvision不能直接使用pip安装
```bash
pip install torch torchvision kornia
```

## 使用方法

1. 把包添加到工作空间后编译：

```bash
cd ~/loader_ws
colcon build --packages-select calibration_validator
source install/setup.bash
```

2. 运行标定验证节点：

```bash
ros2 run calibration_validator calibration_validator_node
```
或者
```bash
ros2 launch calibration_validator calibration_validator.launch.py
```
## 配置参数

节点中已经默认配置了相机到激光雷达的变换矩阵，如果需要测试其他变换参数，可以直接修改代码中的雷达到相机变换部分。

## 订阅话题

- `/camera/color/camera_info` - 相机内参信息
- `/camera/color/image_raw` - 相机图像数据
- `/rslidar_points` - 激光雷达点云数据

## 发布话题

- `/calibration_validation_result` - 标定验证可视化结果

## 使用提示

- 彩色点表示点云深度，红色为近距离，蓝色为远距离
- 图片右侧有深度颜色图例，显示最大和最小深度值
- 如果标定准确，点云应该准确地落在对应物体上（例如，地面点应在地面图像上，墙面点应在墙面图像上） 