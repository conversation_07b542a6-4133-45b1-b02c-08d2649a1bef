from launch import LaunchDescription
from launch_ros.actions import Node
from launch.actions import ExecuteProcess
from launch.substitutions import LaunchConfiguration
from launch.actions import DeclareLaunchArgument
from ament_index_python.packages import get_package_share_directory
import os
import yaml

def generate_launch_description():
    # 获取包路径
    package_dir = get_package_share_directory('calibration_validator')

    # 配置文件路径
    config_file = os.path.join(package_dir, 'config', 'calibration_params.yaml')

    # 声明启动参数
    camera_frame = LaunchConfiguration("camera_frame")
    camera_frame_cmd = DeclareLaunchArgument(
        "camera_frame",
        default_value="camera_frame",
        description="坐标系：相机坐标系名称",
    )

    lidar_frame = LaunchConfiguration("lidar_frame")
    lidar_frame_cmd = DeclareLaunchArgument(
        "lidar_frame",
        default_value="livox_frame",
        description="坐标系：激光雷达坐标系名称",
    )

    camera_info_topic = LaunchConfiguration("camera_info_topic")
    camera_info_topic_cmd = DeclareLaunchArgument(
        "camera_info_topic",
        default_value="/camera/color/camera_info",
        description="话题：相机信息话题",
    )

    camera_image_topic = LaunchConfiguration("camera_image_topic")
    camera_image_topic_cmd = DeclareLaunchArgument(
        "camera_image_topic",
        default_value="/camera/image_raw",
        description="话题：相机图像话题",
    )

    lidar_topic = LaunchConfiguration("lidar_topic")
    lidar_topic_cmd = DeclareLaunchArgument(
        "lidar_topic",
        default_value="/livox/lidar",
        description="话题：雷达点云话题",
    )

    # 标定验证节点 - 使用ROS2推荐的参数加载方式
    calibration_validator_node = Node(
        package='calibration_validator',
        executable='calibration_validator_node',
        name='calibration_validator',
        output='screen',
        parameters=[
            config_file,  # 从配置文件加载外参矩阵
            {
                'camera_frame': camera_frame,
                'lidar_frame': lidar_frame,
                'camera_info_topic': camera_info_topic,
                'camera_image_topic': camera_image_topic,
                'lidar_topic': lidar_topic,
            }
        ]
    )

    # # rviz2 可视化
    # rviz_node = Node(
    #     package='rviz2',
    #     executable='rviz2',
    #     name='rviz2',
    #     arguments=['-d', '/root/loader_ws/src/loader_perception/calibration_validator/launch/calibration_validator.rviz'],
    #     output='screen'
    # )

    # # rqt_image_view 用于查看验证结果图像
    # rqt_image_view = ExecuteProcess(
    #     cmd=['ros2', 'run', 'rqt_image_view', 'rqt_image_view'],
    #     output='screen'
    # )

    return LaunchDescription([
        # 声明所有参数
        camera_frame_cmd,
        lidar_frame_cmd,
        camera_info_topic_cmd,
        camera_image_topic_cmd,
        lidar_topic_cmd,
        # 节点
        calibration_validator_node,
        # rqt_image_view
    ])