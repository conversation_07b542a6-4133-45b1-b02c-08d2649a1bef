#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import rclpy
from rclpy.node import Node
from sensor_msgs.msg import Image, CameraInfo, PointCloud2
from geometry_msgs.msg import TransformStamped
import message_filters
from cv_bridge import CvBridge
import numpy as np
import torch
import kornia as K
from kornia.geometry.linalg import transform_points
import cv2
from tf2_ros import Buffer, TransformListener
import tf2_ros
import struct
import time
from scipy.spatial.transform import Rotation
from collections import deque
import statistics

class CalibrationValidator(Node):
    def __init__(self):
        super().__init__('calibration_validator')

        # 从ROS参数获取配置
        self.camera_frame = self.declare_parameter('camera_frame', 'camera_frame').value
        self.lidar_frame = self.declare_parameter('lidar_frame', 'livox_frame').value
        camera_info_topic = self.declare_parameter('camera_info_topic', '/camera/color/camera_info').value
        camera_image_topic = self.declare_parameter('camera_image_topic', '/camera/image_raw').value
        lidar_topic = self.declare_parameter('lidar_topic', '/livox/lidar').value

        # 从ROS参数获取外参矩阵，如果未提供则使用默认值
        default_extrinsic = [
            0.0065, -0.9999, 0.0139, 0.0375,
            -0.0045, -0.0139, -0.9999, -0.1166,
            1.0000, 0.0065, -0.0046, -0.1435,
            0.0000, 0.0000, 0.0000, 1.0000
        ]
        extrinsic_matrix = self.declare_parameter('extrinsic_matrix', default_extrinsic).value
        self.get_logger().info(f'使用外参矩阵: {extrinsic_matrix}')

        # 初始化CV桥
        self.bridge = CvBridge()

        # 设备配置 - 使用CUDA
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        # self.device = torch.device('cpu')
        self.get_logger().info(f'使用设备: {self.device}')

        # 创建TF监听器
        self.tf_buffer = Buffer()
        self.tf_listener = TransformListener(self.tf_buffer, self)

        # 相机内参
        self.K = None
        self.D = None

        # 相机信息订阅
        self.camera_info_sub = self.create_subscription(
            CameraInfo,
            camera_info_topic,
            self.camera_info_callback,
            10)

        # 使用近似时间同步订阅图像和点云
        self.image_sub = message_filters.Subscriber(self, Image, camera_image_topic)
        self.pointcloud_sub = message_filters.Subscriber(self, PointCloud2, lidar_topic)

        # 设置同步策略
        self.sync = message_filters.ApproximateTimeSynchronizer(
            [self.image_sub, self.pointcloud_sub], 10, 0.1)
        self.sync.registerCallback(self.sync_callback)

        # 发布可视化结果
        self.viz_pub = self.create_publisher(Image, '/calibration_validation_result', 10)

        # 将一维参数列表转换为4x4矩阵
        self.rotation_translation_matrix = np.array(extrinsic_matrix).reshape(4, 4)

        # 预计算并缓存变换矩阵到PyTorch张量
        self.rotation_matrix = torch.tensor(
            self.rotation_translation_matrix[:3, :3],
            dtype=torch.float32,
            device=self.device
        )
        self.translation_vector = torch.tensor(
            self.rotation_translation_matrix[:3, 3],
            dtype=torch.float32,
            device=self.device
        )

        # 仅为兼容性保留TF对象，实际转换将直接使用矩阵
        self.tf_lidar_to_camera = TransformStamped()
        self.tf_lidar_to_camera.header.frame_id = self.lidar_frame
        self.tf_lidar_to_camera.child_frame_id = 'camera'

        # 性能统计 - 用于记录各阶段的执行时间
        self.enable_timing = True  # 可以通过ROS参数控制
        self.timing_window_size = 50  # 统计最近50次的时间
        self.timings = {
            'image_conversion': deque(maxlen=self.timing_window_size),
            'point_extraction': deque(maxlen=self.timing_window_size),
            'point_filtering': deque(maxlen=self.timing_window_size),
            'transform_to_camera': deque(maxlen=self.timing_window_size),
            'projection': deque(maxlen=self.timing_window_size),
            'visualization': deque(maxlen=self.timing_window_size),
            'publish': deque(maxlen=self.timing_window_size),
            'total': deque(maxlen=self.timing_window_size)
        }


        self.get_logger().info('校准验证节点已初始化')
        self.get_logger().info(f'相机坐标系: {self.camera_frame}')
        self.get_logger().info(f'雷达坐标系: {self.lidar_frame}')
        self.get_logger().info(f'相机信息话题: {camera_info_topic}')
        self.get_logger().info(f'相机图像话题: {camera_image_topic}')
        self.get_logger().info(f'雷达点云话题: {lidar_topic}')

    def camera_info_callback(self, msg):
        if self.K is not None:
            return

        # 提取相机内参矩阵
        self.K = np.array(msg.k).reshape(3, 3)
        self.D = np.array(msg.d)

        # 转换为PyTorch张量
        self.K_tensor = torch.tensor(self.K, dtype=torch.float32, device=self.device)
        self.D_tensor = torch.tensor(self.D, dtype=torch.float32, device=self.device)

        # 注释或删除不存在的CameraMatrix API调用
        # self.camera_matrix = K.geometry.camera.CameraMatrix(self.K_tensor)

        self.get_logger().info('已获取相机内参')

    def sync_callback(self, image_msg, pointcloud_msg):
        if self.K is None:
            self.get_logger().warn('等待相机内参...')
            return

        try:
            total_start_time = time.time()
            timing_data = {}

            # 转换图像
            t_start = time.time()
            cv_image = self.bridge.imgmsg_to_cv2(image_msg, desired_encoding='bgr8')
            height, width = cv_image.shape[:2]
            if self.enable_timing:
                self.timings['image_conversion'].append(time.time() - t_start)
                timing_data['图像转换'] = time.time() - t_start

            # 处理点云数据 - 高效提取
            t_start = time.time()
            points_3d = self.extract_xyz_from_pointcloud2(pointcloud_msg)
            if self.enable_timing:
                self.timings['point_extraction'].append(time.time() - t_start)
                timing_data['点云提取'] = time.time() - t_start

            # 预先过滤太远的点
            t_start = time.time()
            distances = np.linalg.norm(points_3d, axis=1)
            distance_mask = distances < 30.0
            points_3d = points_3d[distance_mask]
            if self.enable_timing:
                self.timings['point_filtering'].append(time.time() - t_start)
                timing_data['点云过滤'] = time.time() - t_start

            if len(points_3d) == 0:
                self.get_logger().warn('没有有效的点云数据')
                return

            # 批量处理点云 - 使用torch和kornia进行高效转换
            t_start = time.time()
            camera_points = self.transform_points_to_camera_efficient(points_3d)

            # 过滤掉相机后方的点 - 在GPU上进行
            front_mask = camera_points[:, 2] > 0
            camera_points_filtered = camera_points[front_mask]
            if self.enable_timing:
                self.timings['transform_to_camera'].append(time.time() - t_start)
                timing_data['坐标转换'] = time.time() - t_start

            if len(camera_points_filtered) == 0:
                self.get_logger().warn('没有点位于相机视野内')
                return

            # 投影到图像平面 - 使用kornia的投影功能
            t_start = time.time()
            image_points, in_image = self.project_points_to_image_efficient(
                camera_points_filtered, width, height
            )

            # 只保留在图像内的点
            valid_points = image_points[in_image]
            valid_depths = camera_points_filtered[in_image, 2].cpu().numpy()
            if self.enable_timing:
                self.timings['projection'].append(time.time() - t_start)
                timing_data['点云投影'] = time.time() - t_start

            if len(valid_points) == 0:
                self.get_logger().warn('没有点投影到图像内')
                return

            # 绘制投影结果
            t_start = time.time()
            result_image = self.visualize_projection_efficient(
                cv_image, valid_points, valid_depths
            )
            if self.enable_timing:
                self.timings['visualization'].append(time.time() - t_start)
                timing_data['可视化'] = time.time() - t_start

            # 发布结果
            t_start = time.time()
            result_msg = self.bridge.cv2_to_imgmsg(result_image, encoding="bgr8")
            result_msg.header.stamp = self.get_clock().now().to_msg()
            result_msg.header.frame_id = self.camera_frame
            self.viz_pub.publish(result_msg)
            if self.enable_timing:
                self.timings['publish'].append(time.time() - t_start)
                timing_data['发布结果'] = time.time() - t_start

            # 记录总处理时间
            total_time = time.time() - total_start_time
            if self.enable_timing:
                self.timings['total'].append(total_time)

            # 输出详细的本次处理时间
            timing_str = " | ".join([f"{k}: {v*1000:.1f}ms ({v/total_time*100:.1f}%)" for k, v in timing_data.items()])
            self.get_logger().info(f"总处理时间: {total_time*1000:.1f}ms | {timing_str}")

        except Exception as e:
            self.get_logger().error(f'处理失败: {str(e)}')

    def extract_xyz_from_pointcloud2(self, pointcloud_msg):
        """使用numpy向量化操作提取点云数据"""
        # 获取点云结构信息
        offset_dict = {field.name: field.offset for field in pointcloud_msg.fields}
        point_step = pointcloud_msg.point_step

        # 将整个点云数据转换为numpy数组，避免逐点处理
        cloud_array = np.frombuffer(pointcloud_msg.data, dtype=np.uint8).reshape(-1, point_step)

        # 一次性提取所有x,y,z值
        x = np.frombuffer(cloud_array[:, offset_dict['x']:offset_dict['x']+4].tobytes(), dtype=np.float32)
        y = np.frombuffer(cloud_array[:, offset_dict['y']:offset_dict['y']+4].tobytes(), dtype=np.float32)
        z = np.frombuffer(cloud_array[:, offset_dict['z']:offset_dict['z']+4].tobytes(), dtype=np.float32)

        # 合并并过滤无效点
        points = np.column_stack((x, y, z))
        valid_mask = ~np.isnan(points).any(axis=1)

        return points[valid_mask]

    def transform_points_to_camera_efficient(self, points_3d):
        """使用kornia高效转换点云坐标系"""
        t_start = time.time()

        # 将点云数据转换为torch张量并移至GPU
        points_tensor = torch.tensor(points_3d, dtype=torch.float32, device=self.device)

        # 使用kornia.geometry.linalg.transform_points进行高效变换
        # 首先创建包含旋转和平移的4x4变换矩阵
        transformation = torch.eye(4, dtype=torch.float32, device=self.device)
        transformation[:3, :3] = self.rotation_matrix
        transformation[:3, 3] = self.translation_vector

        # 扩展点为齐次坐标
        points_homo = torch.ones((points_tensor.shape[0], 4), dtype=torch.float32, device=self.device)
        points_homo[:, :3] = points_tensor

        # 批量矩阵乘法
        transformed_points_homo = torch.matmul(points_homo, transformation.t())

        # 返回非齐次坐标
        result = transformed_points_homo[:, :3]

        if self.enable_timing:
            processing_time = time.time() - t_start
            self.get_logger().debug(f'点云坐标转换时间: {processing_time*1000:.2f}ms, 点数: {len(points_3d)}')

        return result

    def project_points_to_image_efficient(self, camera_points, width, height):
        """使用kornia高效投影3D点到图像平面"""
        t_start = time.time()

        # 使用Kornia的投影函数 - 修复API调用，使用正确的参数数量
        # 在最新版本的kornia中，project_points只接受两个参数
        # 由于没有畸变参数，直接使用相机内参进行投影
        point_2d = K.geometry.project_points(
            camera_points,
            self.K_tensor.to(self.device)
        )

        # 检查哪些点在图像内
        in_image = ((point_2d[:, 0] >= 0) &
                   (point_2d[:, 0] < width) &
                   (point_2d[:, 1] >= 0) &
                   (point_2d[:, 1] < height))

        if self.enable_timing:
            processing_time = time.time() - t_start
            self.get_logger().debug(f'点云投影时间: {processing_time*1000:.2f}ms, 投影点数: {torch.sum(in_image).item()}/{len(camera_points)}')

        return point_2d, in_image

    def visualize_projection_efficient(self, image, image_points, depths):
        """批量绘制点而非逐点绘制"""
        result = image.copy()

        # 使用numpy批量计算颜色
        norm_depths = (depths - np.min(depths)) / (np.max(depths) - np.min(depths) + 1e-10)
        hues = (norm_depths * 180).astype(np.uint8)

        # 预先创建颜色数组
        colors = np.zeros((len(hues), 3), dtype=np.uint8)
        colors[:, 0] = hues  # H通道
        colors[:, 1] = 255   # S通道
        colors[:, 2] = 255   # V通道

        # 转换HSV到BGR
        bgr_colors = cv2.cvtColor(colors.reshape(-1, 1, 3), cv2.COLOR_HSV2BGR).reshape(-1, 3)

        # 将点坐标转换为整数并绘制
        coords = image_points.cpu().numpy().astype(np.int32)

        # 每次批量绘制一部分点，避免一次处理过多
        batch_size = 1000
        for i in range(0, len(coords), batch_size):
            end = min(i + batch_size, len(coords))
            for j in range(i, end):
                cv2.circle(result, (coords[j][0], coords[j][1]), 1,
                          (int(bgr_colors[j][0]), int(bgr_colors[j][1]), int(bgr_colors[j][2])), -1)

        # 添加颜色深度图例
        h, w = result.shape[:2]
        bar_width = 20
        bar_height = 100
        bar_x = w - bar_width - 10
        bar_y = 10

        # 绘制颜色条 - 使用numpy操作提高效率
        color_bar = np.zeros((bar_height, bar_width, 3), dtype=np.uint8)
        for i in range(bar_height):
            hue = int((1.0 - i / bar_height) * 180)
            color = cv2.cvtColor(np.uint8([[[hue, 255, 255]]]), cv2.COLOR_HSV2BGR)[0, 0]
            color_bar[i, :] = color

        # 将颜色条放入结果图像
        result[bar_y:bar_y+bar_height, bar_x:bar_x+bar_width] = color_bar

        # 添加最大最小深度标签
        cv2.putText(result, f"{np.max(depths):.1f}m", (bar_x - 30, bar_y + 10), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
        cv2.putText(result, f"{np.min(depths):.1f}m", (bar_x - 30, bar_y + bar_height - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
        cv2.putText(result, "深度", (bar_x - 10, bar_y - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)

        return result

def main(args=None):
    rclpy.init(args=args)

    node = CalibrationValidator()

    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()