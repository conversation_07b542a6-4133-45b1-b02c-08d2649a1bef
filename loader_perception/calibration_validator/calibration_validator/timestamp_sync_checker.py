#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import rclpy
from rclpy.node import Node
from sensor_msgs.msg import Image, PointCloud2

class TimestampSyncChecker(Node):
    """
    一个简单的ROS2节点，用于检测图像和点云消息的时间戳差异，并根据设定容差进行输出
    """
    def __init__(self):
        super().__init__('timestamp_sync_checker')
        # 声明参数
        self.declare_parameter('image_topic', '/camera/image_raw')
        self.declare_parameter('lidar_topic', '/livox/lidar')
        self.declare_parameter('tolerance', 0.05)  # 时间戳容差，单位秒

        image_topic = self.get_parameter('image_topic').value
        lidar_topic = self.get_parameter('lidar_topic').value
        self.tolerance = self.get_parameter('tolerance').value

        self.get_logger().info(f'订阅图像话题: {image_topic}')
        self.get_logger().info(f'订阅点云话题: {lidar_topic}')
        self.get_logger().info(f'时间戳容差: {self.tolerance}s')

        # 初始化最新时间戳
        self.image_stamp = None
        self.lidar_stamp = None

        # 分别订阅图像和点云
        self.create_subscription(Image, image_topic, self.image_callback, 10)
        self.create_subscription(PointCloud2, lidar_topic, self.lidar_callback, 10)

    def image_callback(self, msg: Image):
        # 打印图像宽度和高度
        self.get_logger().info(f'接收到图像: 宽度 {msg.width}, 高度 {msg.height}')
        # 检测标准格式(header.stamp)是否缺失
        if msg.header.stamp.sec == 0 and msg.header.stamp.nanosec == 0:
            self.get_logger().warn('图像消息缺少标准时间戳(header.stamp)，无法进行同步检测')
            return
        # 获取图像消息时间戳（秒）
        self.image_stamp = msg.header.stamp.sec + msg.header.stamp.nanosec * 1e-9
        self.check_timestamp_diff()

    def lidar_callback(self, msg: PointCloud2):
        # 检测标准格式(header.stamp)是否缺失
        if msg.header.stamp.sec == 0 and msg.header.stamp.nanosec == 0:
            self.get_logger().warn('点云消息缺少标准时间戳(header.stamp)，无法进行同步检测')
            return
        # 获取点云消息时间戳（秒）
        self.lidar_stamp = msg.header.stamp.sec + msg.header.stamp.nanosec * 1e-9
        self.check_timestamp_diff()

    def check_timestamp_diff(self):
        # 当两个话题都已接收到至少一次消息时进行比较
        if self.image_stamp is None or self.lidar_stamp is None:
            return
        diff = self.image_stamp - self.lidar_stamp
        diff_abs = abs(diff)
        ms = diff_abs * 1000
        if diff_abs <= self.tolerance:
            self.get_logger().info(
                f'时间戳同步: 差值 {diff:.6f}s ({ms:.2f}ms) 在容差范围内'
            )
        else:
            self.get_logger().warn(
                f'时间戳不同步: 差值 {diff:.6f}s ({ms:.2f}ms) 超过容差 {self.tolerance}s'
            )


def main(args=None):
    rclpy.init(args=args)
    node = TimestampSyncChecker()
    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        node.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main() 