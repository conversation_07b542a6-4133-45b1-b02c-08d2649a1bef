<robot xmlns:xacro="http://www.ros.org/wiki/xacro">
    <xacro:include filename="$(find fishbot_description)/urdf/fishbot/common_inertia.xacro"/>
    <xacro:macro name="camera_xacro" params="xyz">
        <!--相机模块-->
        <link name="camera_link">
            <visual>
                <origin xyz="0 0 0.0" rpy="0 0 0" />
                <geometry>
                    <box size="0.02 0.10 0.02" />
                </geometry>
                <material name="green">
                    <color rgba="0.0 1.0 0.0 0.8" />
                </material>
            </visual>
            <collision>
                <origin xyz="0 0 0.0" rpy="0 0 0" />
                <geometry>
                    <box size="0.02 0.10 0.02" />
                </geometry>
            </collision>
            <xacro:box_inertia m="1.0" w="0.02" h="0.10" d="0.02"/>
        </link>

        <joint name="camera_joint" type="fixed">
            <parent link="base_link" />
            <child link="camera_link" />
            <origin xyz="${xyz}" />
        </joint>
        <!--添加虚拟部件 camera_optical_link，用于相机方向矫正-->
        <link name="camera_optical_link"></link>
        <joint name="camera_optical_joint" type="fixed">
            <parent link="camera_link"/>
            <child link="camera_optical_link"/>
            <!-- 通过修改关节的rpy，来矫正相机关节的方向 -->
            <origin xyz="0 0 0" rpy="${-pi/2} 0 ${-pi/2}"/>
        </joint>
    </xacro:macro>
</robot>