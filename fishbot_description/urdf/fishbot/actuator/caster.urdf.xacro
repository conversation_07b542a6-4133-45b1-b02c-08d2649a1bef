<?xml version="1.0"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro">
    <xacro:include filename="$(find fishbot_description)/urdf/fishbot/common_inertia.xacro"/>
    <xacro:macro name="caster_xacro" params="caster_name xyz">
        <gazebo reference="${caster_name}_caster_link">
            <mu1 value="0.0"/>
            <mu2 value="0.0"/>
            <kp value="1000000000.0"/>
            <kd value="1.0"/>
        </gazebo>
        <link name="${caster_name}_caster_link">
            <visual>
                <origin xyz="0 0 0.0" rpy="0 0 0"/>
                <geometry>
                    <!--万向轮几何形状采用圆球形状-->
                    <sphere radius="0.016"/>
                </geometry>
                <material name="yellow">
                    <color rgba="1.0 1.0 0.0 0.8"/>
                </material>
            </visual>
            <collision>
                <origin xyz="0 0 0.0" rpy="0 0 0"/>
                <geometry>
                    <sphere radius="0.016"/>
                </geometry>
            </collision>
            <xacro:sphere_inertia m="1.0" r="0.016"/>
        </link>

        <!--万向轮关节固定，旋转轴设为"0 0 0"-->
        <joint name="${caster_name}_caster_joint" type="fixed">
            <parent link="base_link" />
            <child link="${caster_name}_caster_link" />
            <origin xyz="${xyz}" />
            <axis xyz="0 0 0"/>
        </joint>
    </xacro:macro>
</robot>