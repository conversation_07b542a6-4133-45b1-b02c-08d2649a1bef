<?xml version="1.0"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro">
    <!--定义名为 wheel_xacro 的宏，参数为轮子名称和固定位置-->
    <xacro:include filename="$(find fishbot_description)/urdf/fishbot/common_inertia.xacro"/>
    <xacro:macro name="wheel_xacro" params="wheel_name xyz">
        <!--修改物理属性-->
        <gazebo reference="${wheel_name}_wheel_link">
            <!-- mu1切向摩擦系数，mu2法向摩擦系数，kp接触刚度系数，kd阻尼系数-->
            <mu1 value="20.0"/>
            <mu2 value="20.0"/>
            <kp value="1000000000.0"/>
            <kd value="1.0"/>
        </gazebo>
        <link name="${wheel_name}_wheel_link">
            <visual>
                <!--默认轮子为躺平状态，所以将rpy的r值调整为 1.57079 rad (90°)-->
                <origin xyz="0 0 0.0" rpy="1.57079 0 0"/>
                <geometry>
                    <cylinder length="0.04" radius="0.032"/>
                </geometry>
                <material name="yellow">
                    <color rgba="1.0 1.0 0.0 0.8"/>
                </material>
            </visual>
            <collision>
                <origin xyz="0 0 0.0" rpy="1.57079 0 0"/>
                <geometry>
                    <cylinder length="0.04" radius="0.032"/>
                </geometry>
            </collision>
            <xacro:cylinder_inertia m="1.0" r="0.032" h="0.04"/>
        </link>

        <!--连续关节continuous，可绕着某个轴无限制旋转。（除了固定、连续，还有旋转、浮动、平面关节）-->
        <joint name="${wheel_name}_wheel_joint" type="continuous">
            <parent link="base_link" />
            <child link="${wheel_name}_wheel_link" />
            <origin xyz="${xyz}" />
            <!--axis子标签，表示旋转轴和方向。"0 1 0" 表示绕y轴正方向旋转-->
            <axis xyz="0 1 0"/>
        </joint>
    </xacro:macro>
</robot>