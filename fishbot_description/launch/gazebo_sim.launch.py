import os
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, IncludeLaunchDescription, SetEnvironmentVariable
from launch.substitutions import LaunchConfiguration, Command, EnvironmentVariable
from launch_ros.actions import Node
from launch.launch_description_sources import PythonLaunchDescriptionSource

def generate_launch_description():
    # 获取功能包的共享目录路径
    pkg_path = get_package_share_directory('fishbot_description')
    
    # 定义 URDF 文件路径
    urdf_file_path = os.path.join(pkg_path, 'urdf', 'fishbot', 'fishbot.urdf')
    
    # 确保文件存在
    if not os.path.exists(urdf_file_path):
        raise FileNotFoundError(f"URDF 文件不存在: {urdf_file_path}")
    
    # 读取 URDF 文件内容
    with open(urdf_file_path, 'r') as file:
        robot_description = file.read()
    
    world_file_path = os.path.join(pkg_path, 'world', 'custom_room.world')
    
    # 启动 Gazebo
    gazebo = IncludeLaunchDescription(
        PythonLaunchDescriptionSource([os.path.join(
            get_package_share_directory('gazebo_ros'), 'launch', 'gazebo.launch.py')]),
        launch_arguments={'world': world_file_path}.items()
    )
    
    # 创建机器人状态发布节点
    robot_state_publisher_node = Node(
        package='robot_state_publisher',
        executable='robot_state_publisher',
        parameters=[{'robot_description': robot_description}],
        output='screen'
    )
    
    # 创建关节状态发布节点
    joint_state_publisher_node = Node(
        package='joint_state_publisher',
        executable='joint_state_publisher',
        parameters=[{'robot_description': robot_description}],
        output='screen'
    )
    
    # 将机器人模型生成到Gazebo中
    spawn_entity = Node(
        package='gazebo_ros',
        executable='spawn_entity.py',
        arguments=['-entity', 'fishbot', '-topic', 'robot_description'],
        output='screen'
    )
    
    return LaunchDescription([
        robot_state_publisher_node,
        joint_state_publisher_node,
        gazebo,
        spawn_entity
    ]) 