#!/usr/bin/env python3
import numpy as np
# 添加兼容性修复
if not hasattr(np, 'float'):
    np.float = np.float64

from visualization_msgs.msg import Marker
from geometry_msgs.msg import Pose, Transform, TransformStamped
import os
from ament_index_python.packages import get_package_share_directory
import rclpy
from rclpy.node import Node
import tf2_ros
from tf2_ros import TransformBroadcaster
from tf_transformations import quaternion_matrix, quaternion_from_matrix

class GazeboWorldPublisher(Node):
    def __init__(self):
        super().__init__('gazebo_world_publisher')
        
        self.last_stamp = self.get_clock().now()
        
        # Default variables
        try:
            pkg_path = get_package_share_directory('fishbot_description')
            default_model_file = f"{pkg_path}/world/Media/models/outdoor.dae"
        except Exception as e:
            self.get_logger().error(f"Package not found: {e}")
            default_model_file = ""
        
        # Initialize tf
        self.tf_buffer = tf2_ros.Buffer()
        self.tf_listener = tf2_ros.TransformListener(self.tf_buffer, self)
        self.broadcaster = TransformBroadcaster(self)
        
        # Create marker
        self.marker = Marker()
        self.marker.header.frame_id = "world"
        self.marker.id = 0
        self.marker.ns = "world"
        self.marker.action = Marker.ADD
        self.marker.scale.x = 1.0
        self.marker.scale.y = 1.0
        self.marker.scale.z = 1.0
        self.marker.color.a = 1.0
        self.marker.pose.orientation.w = 1.0
        self.marker.mesh_use_embedded_materials = True
        self.marker.type = Marker.MESH_RESOURCE
        self.marker.mesh_resource = f"file://{default_model_file}"
        
        # Set publisher
        self.pub = self.create_publisher(
            Marker, 
            "/fishbot_description/simulation_world", 
            10
        )
        
        # 使用定时器代替 Gazebo 话题订阅
        self.timer = self.create_timer(0.1, self.tf_callback)  # 10Hz 更新
        
        self.get_logger().info("[gazebo_world_publisher] Published world!")
    
    def msg_to_se3(self, msg):
        """Conversion from geometric ROS messages into SE(3)"""
        if isinstance(msg, Pose):
            p = np.array([msg.position.x, msg.position.y, msg.position.z])
            q = np.array([msg.orientation.x, msg.orientation.y, msg.orientation.z, msg.orientation.w])
        elif isinstance(msg, Transform):
            p = np.array([msg.translation.x, msg.translation.y, msg.translation.z])
            q = np.array([msg.rotation.x, msg.rotation.y, msg.rotation.z, msg.rotation.w])
        elif isinstance(msg, TransformStamped):
            p = np.array([msg.transform.translation.x, msg.transform.translation.y, msg.transform.translation.z])
            q = np.array(
                [msg.transform.rotation.x, msg.transform.rotation.y, msg.transform.rotation.z, msg.transform.rotation.w]
            )
        norm = np.linalg.norm(q)
        if np.abs(norm - 1.0) > 1e-3:
            raise ValueError(
                f"Received un-normalized quaternion (q = {q} ||q|| = {np.linalg.norm(q):.6f})"
            )
        elif np.abs(norm - 1.0) > 1e-6:
            q = q / norm
            
        g = quaternion_matrix(q)
        g[0:3, -1] = p
        return g
    
    def tf_callback(self):
        stamp = self.get_clock().now()
        if stamp == self.last_stamp:
            return
        
        try:
            # 从 TF 树获取 base_link 相对于 odom 的变换
            # 注意：您可能需要根据实际的 TF 树调整这些框架名称
            transform = self.tf_buffer.lookup_transform(
                'odom',  # 或 'world' - 取决于您的 TF 树
                'base_link',  # 或 'base_footprint'
                rclpy.time.Time(),
                timeout=rclpy.duration.Duration(seconds=0.1)
            )
            
            # 将 TransformStamped 转换为 SE(3) 矩阵
            T_world_base = self.msg_to_se3(transform)
            T_base_world = np.linalg.inv(T_world_base)
            
            # 创建并发布 world 到 base_link 的变换
            t = TransformStamped()
            t.header.stamp = stamp.to_msg()
            t.header.frame_id = "base_link"
            t.child_frame_id = "world"
            t.transform.translation.x = T_base_world[0, 3]
            t.transform.translation.y = T_base_world[1, 3]
            t.transform.translation.z = T_base_world[2, 3]
            
            q = quaternion_from_matrix(T_base_world)
            t.transform.rotation.x = q[0]
            t.transform.rotation.y = q[1]
            t.transform.rotation.z = q[2]
            t.transform.rotation.w = q[3]
            
            self.broadcaster.sendTransform(t)
            
            # Update marker timestamp and publish
            self.marker.header.stamp = stamp.to_msg()
            self.pub.publish(self.marker)
            
        except tf2_ros.TransformException as e:
            self.get_logger().debug(f"Could not get transform: {e}")
        except Exception as e:
            self.get_logger().error(f"Error in tf_callback: {e}")
        
        self.last_stamp = stamp

def main(args=None):
    rclpy.init(args=args)
    node = GazeboWorldPublisher()
    
    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        node.destroy_node()
        rclpy.shutdown()

if __name__ == "__main__":
    main()